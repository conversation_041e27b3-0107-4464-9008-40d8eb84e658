import { useState, useEffect } from "react";
import { getStates } from "../api/global";
import { toast } from "react-toastify";

const stateMasterDataCache = {};

export const useStateMasterData = (countryId) => {
  const [states, setStates] = useState([]);

  useEffect(() => {
    const fetchStates = async () => {
      if (!countryId) return;

      if (stateMasterDataCache[countryId]) {
        setStates(stateMasterDataCache[countryId]);
        return;
      }

      try {
        const res = await getStates(countryId);
        console.log(res)
        let data = res.data || [];
        if (Array.isArray(data)) {
          stateMasterDataCache[countryId] = data;
          setStates(data);
        } else {
          throw new Error("Invalid response");
        }
      } catch (err) {
        toast.error("Error fetching state data");
      }
    };

    fetchStates();
  }, [countryId]);

  return states;
};
