{"layout": {"sidebar": "Barra lateral", "header": "Encabezado"}, "access_area": {"title": "Área de Acceso", "area_name": "Nombre del Área", "pacs_area_name": "Nombre del Área PACS", "facility": "Instalación", "system": "Sistema", "online": "En línea", "requestable_in_self_service": "Solicitable en Autoservicio", "area_types": "Tipo(s) de Área", "card_types": "Tipo(s) de Tarjeta", "status": "Estado", "status_active": "Activo", "status_deleted": "Eliminado", "filter_all": "Todos", "filter_active": "Activo", "filter_deleted": "Eliminado"}, "access_area_details": {"title": "Área de Acceso", "area_name": "Nombre del Área", "creation_date": "Fecha de Creación", "area_type": "Tipo de Área", "requestable_in_self_service": "Solicitable en Autoservicio", "tab_area_details": "Detalles del Área", "tab_facility": "Instalación(es)", "tab_owners_approvers": "Propietarios y Aprobadores", "tab_perimeter_areas": "<PERSON><PERSON><PERSON>", "tab_assigned_identities": "Identidades Asignadas"}, "access_area_group": {"title": "Grupo de Acceso", "area_group_name": "Nombre del Grupo de Área", "type": "Tipo", "creation": "Creación", "requestable_in_self_service": "Solicitable en Autoservicio", "status": "Estado", "status_active": "Activo", "status_deleted": "Eliminado", "filter_all": "Todos", "filter_active": "Activo", "filter_deleted": "Eliminado"}, "access_area_group_details": {"title": "Grupo de Acceso", "area_name": "Nombre del Área", "creation_date": "Fecha de creación", "requestable_in_self_service": "Solicitable en Autoservicio", "status": "Estado", "tab_group_details": "Detalles del Grupo", "tab_areas": "<PERSON><PERSON><PERSON>", "tab_owners_approvers": "Propietarios y Aprobadores", "tab_assigned_identities": "Identidades Asignadas"}, "chat_modal": {"title": "Cha<PERSON>", "eid": "EID", "name": "Nombre", "enter_message": "Ingrese su mensaje:", "cancel": "<PERSON><PERSON><PERSON>", "send": "Enviar"}, "credential_details": {"title": "Centro de Credenciales", "name": "ADAM L'THELAN", "eid": "EID", "created_on": "Creado el", "status": "Estado", "status_unprinted_badges": "Credenciales no impresas", "tab_view_request": "<PERSON><PERSON>", "tab_task": "Tarea"}, "credential_hub": {"title": "Centro de Credenciales", "table_title": "Credenciales", "name": "Nombre", "eid": "EID", "type": "Tipo", "badge_template": "Plantilla de Credencial", "created_on": "Creado el", "badging_facility": "Instalación de Credencial", "shipment_address": "Dirección de Envío", "status": "Estado", "status_unprinted": "No Impreso", "status_printed": "Impreso", "status_in_queue": "Credenciales en Cola", "status_completed": "Completado", "status_pending_photo_upload": "Pendiente de Subir Foto", "status_completed_photo_upload": "Foto Subida Completada", "status_print_failed_badges": "Impresión Fallida", "action": "Acción"}, "print_modal": {"title": "Imprimir Credencial", "front": "<PERSON><PERSON>", "back": "Reverso", "default_name": "<PERSON>", "visitor": "Visitante", "expire": "Expira", "info1": "Es un hecho establecido que un lector se distraerá con el contenido legible de una página.", "info2": "Hay muchas variaciones de pasajes de Lorem Ipsum disponibles, pero la mayoría han sufrido alteraciones de alguna forma.", "print": "Imprimir", "cancel": "<PERSON><PERSON><PERSON>"}, "facility": {"title": "Instalación", "name": "Instalación", "address": "Dirección", "state_province": "Estado/Provincia", "country": "<PERSON><PERSON>", "status": "Estado", "status_active": "Activo", "status_inactive": "Inactivo", "type": "Tipo", "open_in_new_window": "Abrir en nueva ventana", "filter_all": "Todos", "filter_active": "Activo", "tab_facility": "Instalación", "tab_building": "Edificio", "tab_floor": "<PERSON><PERSON>", "tab_room": "Habitación", "tab_access_areas": "Áreas de Acceso"}, "doctors_appointment": {"search_patient_placeholder": "Buscar por nombre de paciente, MRN", "search_guest_placeholder": "Buscar invitados", "not_available": "N/D", "name": "Nombre", "guest_pin": "PIN de invitado", "screening": "Detección", "screening_alert": "Alerta de detección", "appointment_time": "Hora de la cita", "arrival_time": "Hora de ll<PERSON>ada", "location": "Ubicación", "action": "Acción", "add_guest": "Agregar invitado", "update_profile_image": "Actualizar imagen de perfil", "capture_picture": "<PERSON><PERSON><PERSON> imagen", "chat": "Cha<PERSON>", "nda": "Acuerdo de confidencialidad", "print": "Imprimir", "print_guest_label": "Imprimir etiqueta de invitado", "check_in": "Registrar entrada", "guest_check_in": "Registro de entrada de invitado", "check_out": "Registrar salida", "guest_check_out": "Registro de salida de invitado", "patient_name": "Nombre del paciente", "doctors_name": "Nombre del médico", "in": "Entrada", "out": "Salida", "camera": "<PERSON><PERSON><PERSON>", "no_results_found": "No se encontraron resultados", "todays_appointments": "Citas de hoy", "all_future_appointments": "Todas las citas futuras"}, "inpatient_visit": {"search_patient_placeholder": "Buscar por nombre del paciente, MRN", "search_guest_placeholder": "Buscar invitados", "add_denied_guest": "Agregar invitado denegado", "enter_first_name": "Ingrese el nombre", "first_name_required": "Nombre *", "enter_last_name": "Ingrese el apellido", "last_name_required": "Apellido *", "date_of_birth": "Fecha de nacimiento", "select_date": "Seleccione una fecha", "enter_phone_number": "Ingrese el número de teléfono", "phone": "Teléfono", "enter_email": "Ingrese el correo electrónico", "email": "Correo electrónico", "enter_denial_reason": "Ingrese la razón de la denegación", "denial_reason_required": "Razón de denegación *", "add_more": "Agregar más", "save": "Guardar", "create_new_visit": "<PERSON><PERSON><PERSON> una nueva visita", "unknown": "Desconocido", "guest": "<PERSON><PERSON><PERSON><PERSON>", "not_available": "N/D"}, "guest": {"all_guests": "Todos los invitados", "today_guest": "Invitad<PERSON> de <PERSON>y", "guest_name": "Nombre del invitado", "email_id": "Correo electrónico", "company": "Empresa", "last_visited": "Última visita", "next_visit": "Próxima visita", "is_private": "¿Es privado?", "total_visits": "Visitas totales", "actions": "Acciones", "view": "<PERSON>er", "edit": "<PERSON><PERSON>", "history": "Historial", "view_guest": "<PERSON><PERSON> invitado", "edit_guest": "<PERSON><PERSON> in<PERSON>", "view_history": "Ver historial"}, "my_access_areas": {"tabs": {"my_areas": "<PERSON><PERSON>"}, "area_name": "Nombre del Área", "type": "Tipo", "start_date": "Fecha de Inicio", "end_date": "<PERSON><PERSON>", "status": "Estado", "assigned": "<PERSON><PERSON><PERSON>"}, "my_audits": {"task_id": "ID de Tarea", "description": "Descripción", "run_id": "ID de Ejecución", "task_owner": "Responsable de la Tarea", "start_date": "Fecha de Inicio", "end_date": "<PERSON><PERSON>", "recommend": "Recomendar", "action": "Acción", "retain": "<PERSON><PERSON><PERSON>", "remove": "Eliminar", "open": "Abrir"}, "my_events": {"event_title": "Título del Evento", "type": "Tipo", "category": "Categoría", "host": "<PERSON><PERSON><PERSON><PERSON>", "escort": "Escolta", "start_date_time": "Fecha y Hora de Inicio", "end_date_time": "<PERSON>cha y Hora de Fin", "status": "Estado", "status_approved": "Aprobado", "actions": "Acciones", "edit": "<PERSON><PERSON>", "block": "Bloquear"}, "my_profile": {"type": "Tipo", "employee": "Empleado", "eid": "EID", "department": "Departamento", "manager": "<PERSON><PERSON><PERSON>", "status": "Estado", "unprinted_badges": "Credenciales no impresas", "tabs": {"identity": "Identidad", "corporate": "Corporativo", "cards": "Credenciales", "access": "Acceso", "delegates": "Delegados", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requests": "Solicitudes"}}, "my_staff": {"name": "Nombre", "uid": "UID", "type": "Tipo", "company": "Empresa", "organization": "Organización", "job_title": "Título del Puesto", "manager": "<PERSON><PERSON><PERSON>", "expiration_date": "Fecha de Expiración", "status": "Estado", "status_approved": "Aprobado", "open": "Abrir", "filter": {"direct_reports": "Reportes Directos", "my_org": "Mi Organización"}}, "my_request": {"request_id": "ID de Solicitud", "type": "Tipo", "requested_by": "Solicitado Por", "created_on": "Creado El", "justification": "Justificación", "requested_for": "Solicitado Para", "items": "Ítems", "status": "Estado", "status_pending": "Pendiente"}, "my_task": {"task_id": "ID de Tarea", "type": "Tipo", "request_for": "Solicitar Para", "items": "Ítem(s)", "request_id": "ID de Solicitud", "requested_by": "Solicitado Por", "created_by": "<PERSON><PERSON><PERSON>", "assignee": "Asignado a", "justification": "Justificación", "recommend": "Recomendar"}, "team_details": {"type": "Tipo", "employee": "Empleado", "eid": "EID", "department": "Departamento", "manager": "<PERSON><PERSON><PERSON>", "status": "Estado", "unprinted_badges": "Credenciales no impresas", "tabs": {"identity": "Identidad", "corporate": "Corporativo", "cards": "Credenciales", "access": "Acceso", "delegates": "Delegados", "vehicles": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "requests": "Solicitudes"}}, "observation": {"name": "Nombre", "added_by": "<PERSON><PERSON><PERSON><PERSON>", "added_on": "<PERSON><PERSON><PERSON><PERSON>", "expiration_date": "Fecha de Expiración", "status": "Estado", "status_active": "Activo", "status_inactive": "Inactivo", "open": "Abrir", "roster_title": "Lista de Observación", "filter_all": "Todos", "filter_active": "Activo"}, "observation_details": {"title": "Lista de Observación", "address": "Dirección", "status": "Estado", "active": "Activo", "phone_number": "Número de Teléfono", "tabs": {"demographic_information": "Información Demográfica", "reason_handling": "Razón y Manejo", "document": "Documento"}}, "patient_hub": {"title": "Centro de Pacientes", "search_patient_placeholder": "Buscar por nombre del paciente, MRN", "filter_all": "Todos", "filter_admitted": "Admitidos", "tabs": {"demographic": "Demográfico", "guest_list": "Lista de Invitados", "friends_family": "Amigos y Familia", "denied_guests": "Invitados Denegados", "hl7_messages": "Mensajes HL7"}, "mrn": "MRN", "type": "Tipo", "status": "Estado", "confidential": "Confidencial", "facility": "Instalación", "effective_date": "Fecha Efectiva", "changed_by": "Modificado Por", "event_type": "Tipo de Evento", "field_changed": "Campo Modificado", "from": "De", "to": "A", "view_history": "Ver Historial", "loading_patient_details": "Cargando detalles del paciente...", "loading_history": "Cargando historial...", "no_history_found": "No se encontró historial para este paciente.", "loading": "Cargando..."}}