/**
 * Template Storage Utility
 * Manages local storage for badge templates with sync capabilities
 */

const STORAGE_PREFIX = 'badge_template_';
const TEMPLATE_METADATA_KEY = 'badge_templates_metadata';

/**
 * Save template data to local storage
 * @param {string} templateId - Badge template ID
 * @param {object} templateData - Template data to save
 */
export const saveTemplateToStorage = (templateId, templateData) => {
  try {
    const storageKey = `${STORAGE_PREFIX}${templateId}`;
    const dataToStore = {
      ...templateData,
      lastModified: new Date().toISOString(),
      locallyModified: true,
      isDraft: true, // Mark as draft when saved locally
      content: templateData.content || templateData.config || {
        canvasConfig: templateData.canvasConfig || {},
        elements: templateData.elements || []
      }
    };

    // Remove old config structure if it exists
    if (dataToStore.config && dataToStore.content) {
      delete dataToStore.config;
    }
    if (dataToStore.canvasConfig && dataToStore.content) {
      delete dataToStore.canvasConfig;
    }
    if (dataToStore.elements && dataToStore.content) {
      delete dataToStore.elements;
    }

    localStorage.setItem(storageKey, JSON.stringify(dataToStore));

    // Update metadata
    updateTemplateMetadata(templateId, {
      lastModified: dataToStore.lastModified,
      locallyModified: true,
    });

    return true;
  } catch (error) {
    console.error('Error saving template to storage:', error);
    return false;
  }
};

/**
 * Load template data from local storage
 * @param {string} templateId - Badge template ID
 * @returns {object|null} Template data or null if not found
 */
export const loadTemplateFromStorage = (templateId) => {
  try {
    const storageKey = `${STORAGE_PREFIX}${templateId}`;
    const storedData = localStorage.getItem(storageKey);
    
    if (storedData) {
      return JSON.parse(storedData);
    }
    
    return null;
  } catch (error) {
    console.error('Error loading template from storage:', error);
    return null;
  }
};

/**
 * Check if template exists in local storage
 * @param {string} templateId - Badge template ID
 * @returns {boolean} True if template exists locally
 */
export const templateExistsInStorage = (templateId) => {
  const storageKey = `${STORAGE_PREFIX}${templateId}`;
  return localStorage.getItem(storageKey) !== null;
};

/**
 * Remove template from local storage
 * @param {string} templateId - Badge template ID
 */
export const removeTemplateFromStorage = (templateId) => {
  try {
    const storageKey = `${STORAGE_PREFIX}${templateId}`;
    localStorage.removeItem(storageKey);
    
    // Update metadata
    removeTemplateMetadata(templateId);
    
    return true;
  } catch (error) {
    console.error('Error removing template from storage:', error);
    return false;
  }
};

/**
 * Get all template metadata from local storage
 * @returns {object} Object with template IDs as keys and metadata as values
 */
export const getTemplateMetadata = () => {
  try {
    const metadata = localStorage.getItem(TEMPLATE_METADATA_KEY);
    return metadata ? JSON.parse(metadata) : {};
  } catch (error) {
    console.error('Error getting template metadata:', error);
    return {};
  }
};

/**
 * Update template metadata
 * @param {string} templateId - Badge template ID
 * @param {object} metadata - Metadata to update
 */
export const updateTemplateMetadata = (templateId, metadata) => {
  try {
    const allMetadata = getTemplateMetadata();
    allMetadata[templateId] = {
      ...allMetadata[templateId],
      ...metadata
    };
    
    localStorage.setItem(TEMPLATE_METADATA_KEY, JSON.stringify(allMetadata));
  } catch (error) {
    console.error('Error updating template metadata:', error);
  }
};

/**
 * Remove template metadata
 * @param {string} templateId - Badge template ID
 */
export const removeTemplateMetadata = (templateId) => {
  try {
    const allMetadata = getTemplateMetadata();
    delete allMetadata[templateId];
    
    localStorage.setItem(TEMPLATE_METADATA_KEY, JSON.stringify(allMetadata));
  } catch (error) {
    console.error('Error removing template metadata:', error);
  }
};

/**
 * Check if template has been modified locally since last API sync
 * @param {string} templateId - Badge template ID
 * @param {string} apiLastModified - Last modified timestamp from API
 * @returns {boolean} True if locally modified
 */
export const isTemplateLocallyModified = (templateId, apiLastModified) => {
  const localData = loadTemplateFromStorage(templateId);
  if (!localData) return false;
  
  if (localData.locallyModified) return true;
  
  if (apiLastModified && localData.lastModified) {
    const localTime = new Date(localData.lastModified).getTime();
    const apiTime = new Date(apiLastModified).getTime();
    return localTime > apiTime;
  }
  
  return false;
};

/**
 * Mark template as synced with API (remove local modification flag)
 * @param {string} templateId - Badge template ID
 */
export const markTemplateAsSynced = (templateId) => {
  try {
    const localData = loadTemplateFromStorage(templateId);
    if (localData) {
      localData.locallyModified = false;
      localData.isDraft = false; // Clear draft status when synced
      const storageKey = `${STORAGE_PREFIX}${templateId}`;
      localStorage.setItem(storageKey, JSON.stringify(localData));

      // Update metadata
      updateTemplateMetadata(templateId, {
        locallyModified: false,
        synced: true
      });
    }
  } catch (error) {
    console.error('Error marking template as synced:', error);
  }
};

/**
 * Get all locally stored template IDs
 * @returns {string[]} Array of template IDs
 */
export const getLocalTemplateIds = () => {
  try {
    const keys = Object.keys(localStorage);
    return keys
      .filter(key => key.startsWith(STORAGE_PREFIX))
      .map(key => key.replace(STORAGE_PREFIX, ''));
  } catch (error) {
    console.error('Error getting local template IDs:', error);
    return [];
  }
};

/**
 * Clear all template data from local storage
 */
export const clearAllTemplateStorage = () => {
  try {
    const keys = Object.keys(localStorage);
    keys.forEach(key => {
      if (key.startsWith(STORAGE_PREFIX) || key === TEMPLATE_METADATA_KEY) {
        localStorage.removeItem(key);
      }
    });
    return true;
  } catch (error) {
    console.error('Error clearing template storage:', error);
    return false;
  }
};

/**
 * Get storage usage statistics
 * @returns {object} Storage usage information
 */
export const getStorageStats = () => {
  try {
    const templateIds = getLocalTemplateIds();
    const metadata = getTemplateMetadata();
    
    let totalSize = 0;
    templateIds.forEach(id => {
      const data = localStorage.getItem(`${STORAGE_PREFIX}${id}`);
      if (data) {
        totalSize += data.length;
      }
    });
    
    return {
      templateCount: templateIds.length,
      totalSizeBytes: totalSize,
      totalSizeKB: Math.round(totalSize / 1024),
      modifiedCount: Object.values(metadata).filter(m => m.locallyModified).length
    };
  } catch (error) {
    console.error('Error getting storage stats:', error);
    return {
      templateCount: 0,
      totalSizeBytes: 0,
      totalSizeKB: 0,
      modifiedCount: 0
    };
  }
};

/**
 * Check if template has unsaved changes compared to API data
 * @param {string} templateId - Badge template ID
 * @param {object} apiData - Current API data for comparison
 * @returns {boolean} True if template has unsaved changes
 */
export const hasUnsavedChanges = (templateId, apiData) => {
  try {
    const localData = loadTemplateFromStorage(templateId);
    if (!localData) {
      return false; // No local data means no unsaved changes
    }

    // If local data exists and is marked as draft, it has unsaved changes
    if (localData.isDraft) {
      return true;
    }

    // Compare last modified times if available
    if (apiData.lastModified && localData.lastModified) {
      return new Date(localData.lastModified) > new Date(apiData.lastModified);
    }

    // If no clear indication, assume there are changes if local data exists
    return localData.locallyModified === true;
  } catch (error) {
    console.error('Error checking for unsaved changes:', error);
    return false;
  }
};

/**
 * Get all templates with draft status
 * @returns {Array} Array of template IDs that have draft status
 */
export const getDraftTemplates = () => {
  try {
    const draftTemplates = [];
    const metadata = getTemplateMetadata();

    Object.keys(metadata).forEach(templateId => {
      const localData = loadTemplateFromStorage(templateId);
      if (localData && localData.isDraft) {
        draftTemplates.push({
          id: templateId,
          name: localData.name,
          lastModified: localData.lastModified
        });
      }
    });

    return draftTemplates;
  } catch (error) {
    console.error('Error getting draft templates:', error);
    return [];
  }
};
