// facilitySlice.js
import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  facilities: [],
  selectedFacilityId: null,
  selectedFacilityName: 'Select Facility',
};

const facilitySlice = createSlice({
  name: 'facility',
  initialState,
  reducers: {
    setFacilities: (state, action) => {
      state.facilities = action.payload;
    },
    setSelectedFacility: (state, action) => {
      state.selectedFacilityId = action.payload.id;
      state.selectedFacilityName = action.payload.name;
    },
  },
});

export const { setFacilities, setSelectedFacility } = facilitySlice.actions;
export default facilitySlice.reducer;