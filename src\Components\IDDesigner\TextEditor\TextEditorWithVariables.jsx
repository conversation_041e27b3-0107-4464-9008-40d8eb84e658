import React, { useState, useRef, useEffect } from 'react';
import {
  TextField,
  Menu,
  MenuItem,
  Box,
  Typography
} from '@mui/material';
import VariableChip from './VariableChip';

const TextEditorWithVariables = ({ 
  value = '', 
  onChange, 
  label = 'Text Content',
  helperText = 'Type {{ to insert variables',
  availableVariables = ['name', 'id', 'department', 'email', 'phone', 'position', 'company'],
  multiline = true,
  rows = 2,
  size = 'small'
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [cursorPosition, setCursorPosition] = useState(0);
  const [inputValue, setInputValue] = useState(value);
  const textFieldRef = useRef(null);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  const handleInputChange = (event) => {
    const newValue = event.target.value;
    const cursorPos = event.target.selectionStart;
    
    setInputValue(newValue);
    setCursorPosition(cursorPos);

    // Check if user typed {{ to trigger variable menu
    if (newValue.slice(cursorPos - 2, cursorPos) === '{{') {
      setAnchorEl(event.target);
    } else {
      setAnchorEl(null);
    }

    onChange(newValue);
  };

  const handleVariableSelect = (variable) => {
    const beforeCursor = inputValue.slice(0, cursorPosition - 2); // Remove the {{
    const afterCursor = inputValue.slice(cursorPosition);
    const newValue = `${beforeCursor}{{${variable}}}${afterCursor}`;

    setInputValue(newValue);
    onChange(newValue);
    setAnchorEl(null);

    // Focus back to text field and set cursor position
    setTimeout(() => {
      if (textFieldRef.current) {
        const inputElement = textFieldRef.current.querySelector('input') || textFieldRef.current.querySelector('textarea');
        if (inputElement) {
          const newCursorPos = beforeCursor.length + variable.length + 4; // 4 for {{}}
          inputElement.focus();
          if (inputElement.setSelectionRange) {
            inputElement.setSelectionRange(newCursorPos, newCursorPos);
          }
        }
      }
    }, 0);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Parse text to show variables as chips in helper display
  const parseTextForDisplay = (text) => {
    const parts = [];
    const regex = /\{\{([^}]+)\}\}/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      // Add text before the variable
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: text.slice(lastIndex, match.index)
        });
      }
      
      // Add the variable
      parts.push({
        type: 'variable',
        content: match[1]
      });
      
      lastIndex = match.index + match[0].length;
    }
    
    // Add remaining text
    if (lastIndex < text.length) {
      parts.push({
        type: 'text',
        content: text.slice(lastIndex)
      });
    }
    
    return parts;
  };

  const displayParts = parseTextForDisplay(inputValue);

  return (
    <Box>
      <TextField
        ref={textFieldRef}
        label={label}
        value={inputValue}
        onChange={handleInputChange}
        multiline={multiline}
        rows={rows}
        size={size}
        fullWidth
        helperText={helperText}
      />
      
      {/* Variable Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
        PaperProps={{
          sx: {
            maxHeight: 200,
            minWidth: 150,
          }
        }}
      >
        <MenuItem disabled>
          <Typography variant="caption" color="text.secondary">
            Select Variable:
          </Typography>
        </MenuItem>
        {availableVariables.map((variable) => (
          <MenuItem 
            key={variable} 
            onClick={() => handleVariableSelect(variable)}
            sx={{ fontSize: '0.875rem' }}
          >
            {variable}
          </MenuItem>
        ))}
      </Menu>

      {/* Preview with chips */}
      {displayParts.length > 1 && (
        <Box sx={{ mt: 1, p: 1, backgroundColor: '#f5f5f5', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
            Preview:
          </Typography>
          <Box sx={{ display: 'flex', flexWrap: 'wrap', alignItems: 'center', gap: 0.5 }}>
            {displayParts.map((part, index) => (
              part.type === 'variable' ? (
                <VariableChip 
                  key={index} 
                  variable={part.content} 
                  readOnly={true}
                />
              ) : (
                <Typography 
                  key={index} 
                  variant="body2" 
                  component="span"
                  sx={{ 
                    whiteSpace: 'pre-wrap',
                    wordBreak: 'break-word'
                  }}
                >
                  {part.content}
                </Typography>
              )
            ))}
          </Box>
        </Box>
      )}
    </Box>
  );
};

export default TextEditorWithVariables;
