import React, { useState, useEffect } from 'react';
import Swal from "sweetalert2";
import '../../styles/sweetalert-custom.css';
import GenericTable from "../GenericTable";
import { getHL7Messages } from '../../api/PatientHub';
import TruncatedRow from "../Tooltip/TrucantedRow";
import { FaCopy } from "react-icons/fa";
import ReactDOMServer from "react-dom/server";

const HL7Messages = ({ mrn }) => {
  const [messages, setMessages] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('hl7_message_id');
  const [sortOrder, setSortOrder] = useState('ASC');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const handleRowClick = (message) => {
    Swal.fire({
      html: `
        <div style='text-align: left;'>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <h2 style='font-weight: bold;'>HL7 Message</h2>
            <button 
              id="copy-button" 
              style="cursor: pointer;"
            >
              ${ReactDOMServer.renderToString(<FaCopy className='text-[#4F2683]' />)} 
            </button>
          </div>
          <textarea 
            id="hl7-message-textarea" 
            style="width: 100%; height: 150px; padding: 10px; border: 1px solid #ccc; border-radius: 5px;" 
            readonly
          >${message}</textarea>
        </div>
      `,
      confirmButtonText: "Close",
      customClass: {
        popup: "wider-modal",
      },
      didOpen: () => {
        const copyButton = document.getElementById("copy-button");
        const textarea = document.getElementById("hl7-message-textarea");
        copyButton.addEventListener("click", () => {
          navigator.clipboard.writeText(textarea.value).then(() => {
            Swal.fire({
              icon: "success",
              title: "Copied!",
              text: "The message has been copied to your clipboard.",
              timer: 1500,
              showConfirmButton: false,
            });
          });
        });
      },
    });
  };

  useEffect(() => {
    const fetchMessages = async () => {
      // setIsLoading(true);
      setError(null);
      console.log("Patient MRN:", mrn); // Log the mrn to verify its value
      if (!mrn) {
        setError("Patient MRN is missing. Unable to fetch HL7 messages.");
        setIsLoading(false);
        return;
      }
      try {
        const params = {
          mrn,
          sortBy,
          sortOrder,
          search: searchTerm,
        };
        const data = await getHL7Messages(params);
        console.log("🚀 ~ fetchMessages ~ data:", data);
        console.log("API Response:", data); // Log the API response
        const extractedData = data.data.data; // Extract the nested data array
        console.log("Extracted Data:", extractedData); // Log the extracted data
        if (Array.isArray(extractedData)) {
          setMessages(extractedData);
        } else {
          console.error("Expected an array but received:", extractedData);
          setError("Invalid data format received from the server.");
        }
      } catch (err) {
        console.error("Error fetching HL7 messages:", err);
        setError("Failed to load HL7 messages. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchMessages();
  }, [mrn, sortBy, sortOrder, searchTerm]);

  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleSort = (column, direction) => {
    setSortBy(column.id);
    setSortOrder(direction.toUpperCase());
  };

  const columns = [
    { id: "hl7_message_id", name: "HL7 Message ID", selector: row => <TruncatedRow text={row.hl7_message_id} />, sortable: true },
    { id: "hdr", name: "HDR", selector: row => <TruncatedRow text={row.hdr} />, sortable: true },
    { id: "mrn", name: "MRN", selector: row => <TruncatedRow text={row.mrn} />, sortable: true },
    { id: "message_type", name: "Message Type", selector: row => <TruncatedRow text={row.message_type} />, sortable: true },
    {
      id: "message",
      name: "Message",
      selector: row => {
        const truncateMessage = (text) => {
          if (!text) return '';
          const words = text.split(/\s+/);
          const truncated = words.slice(0, 2).join(' ');
          return words.length > 2 ? `${truncated}...` : truncated;
        };
        return (
          <span
            className="cursor-pointer text-black underline"
            onClick={() => handleRowClick(row.message)}
          >
            {truncateMessage(row.message)}
          </span>
        );
      },
      sortable: true
    },
    { id: "processed_at", name: "Processed At", selector: row => <TruncatedRow text={new Date(row.processed_at).toLocaleString()} />, sortable: true },
  ];

  return (
    <div>
      <div className="flex gap-4 mb-4">
        {['All', 'ADT', 'SUI'].map((type) => (
          <button
            key={type}
            className={`px-4 py-2 rounded ${searchTerm === (type === "All" ? "" : type) ? 'bg-[#4F2683] text-white' : 'bg-gray-200 text-gray-700'}`}
            onClick={() => setSearchTerm(type === "All" ? "" : type)}
          >
            {type}
          </button>
        ))}
      </div>

      {error && <div className="text-red-500 mb-4">{error}</div>}
      {isLoading ? (
        <div>Loading HL7 messages...</div>
      ) : (
        <div className='bg-[#fff] rounded-lg shadow-md'>
          <GenericTable
            title="HL7 Messages"
            searchTerm={searchTerm}
            onSearchChange={handleSearch}
            onSort={handleSort}
            columns={columns}
            data={messages}
            sortServer
            showSearch={true}
            showAddButton={false}
          />
        </div>
      )}
    </div>
  );
};

export default HL7Messages;
