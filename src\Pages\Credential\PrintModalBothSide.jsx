import React from "react";
import { useTranslation } from 'react-i18next';
// import Logo from "../../Images/Logo.svg";
import barcode from '../../Images/Barcode.svg';

const PrintModalBothSide = ({ credential, onClose }) => {
    const { t } = useTranslation();

    const handlePrint = () => {
        const printWindow = window.open("", "_blank");
        printWindow.document.write(`
      <html>
        <head>
          <title>${t('print_modal.title')}</title>
          <link rel="stylesheet" href="print.css">
          <style>
            body {
              font-family: Arial, sans-serif;
              margin: 20px;
              color: #333;
            }
            .badge-print-container {
              display: flex;
              flex-direction: column;
              align-items: center;
              margin: 0 auto;
              width: 320px; /* adjust as needed */
            }
            .card {
              border: 2px dashed #ccc;
              border-radius: 8px;
              width: 100%;
              padding: 20px;
              margin-bottom: 20px;
              position: relative;
              items-center: center;
              justify-content: center;
              display: flex;
                flex-direction: column;
            }
            .card-title {
              font-size: 14px;
              font-weight: bold;
              margin-bottom: 8px;
              color: #4F2683; /* optional color */
              text-transform: uppercase;
            }
             .avatar {
               width: 80px;
               height: 80px;
               border-radius: 50%;
               margin: 0 auto 10px auto;
               display: block;
               object-fit: cover;
             }
            .name-text {
              text-align: center;
              font-size: 16px;
              font-weight: 600;
              margin-bottom: 4px;
            }
            .visitor-text {
              text-align: center;
              font-size: 14px;
              font-weight: 500;
              color: #ffff;
              margin-x: 10px;
              width: 10%;
              background-color: #4F2683;
              margin-bottom: 10px;
            }
            .expire-text {
              text-align: center;
              font-size: 12px;
              color: #666;
              margin-bottom: 10px;
            }
            .barcode {
              display: block;
              margin: 10px auto;
              width: 120px; /* adjust as needed */
              height: auto;
            }
            /* Example paragraphs on back side */
            .back-info {
              font-size: 12px;
              line-height: 1.4;
              margin-bottom: 10px;
            }
            /* Hide all controls for printing, except the badge container */
            @media print {
              body * {
                visibility: hidden;
              }
              .badge-print-container,
              .badge-print-container * {
                visibility: visible;
              }
              .badge-print-container {
                position: absolute;
                left: 0;
                top: 0;
              }
            }
          </style>
        </head>
        <body>
          <div class="badge-print-container">
            <!-- Front Side Card -->
            <div class="card">
              <div class="card-title">${t('print_modal.front')}</div>
              <img class="avatar" src="${credential.image}" alt="Avatar" />
              <div class="name-text">${credential.name || t('print_modal.default_name')}</div>
              <div class="visitor-text">${t('print_modal.visitor')}</div>
              <div class="expire-text">
                ${t('print_modal.expire')}: 2-04-2022 10:00 AM
              </div>
            <img src="${barcode}" alt="barcode" class="barcode-img" />
            </div>

            <!-- Back Side Card -->
            <div class="card">
              <div class="card-title">${t('print_modal.back')}</div>
              <div class="name-text">${credential.name || t('print_modal.default_name')}</div>
              <div class="expire-text">
                ${t('print_modal.expire')}: 2-04-2022 10:00 AM
              </div>
              <div class="back-info">
                ${t('print_modal.info1')}
              </div>
              <div class="back-info">
                ${t('print_modal.info2')}
              </div>
              <img src="${barcode}" alt="barcode" class="barcode-img" />
            </div>
          </div>
        </body>
      </html>
    `);
        printWindow.document.close();
        onClose();
        printWindow.onload = () => {
            printWindow.print();
        };
    };

    return (
        <div className="fixed z-10 inset-0 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white p-6 rounded-lg shadow-lg ">
                <h2 className="text-xl font-bold mb-4">{t('print_modal.title')}</h2>
                {/* Preview of the same design that will be printed */}
                <div className="badge-print-container flex gap-1">
                    {/* Front Side Card Preview */}
                    <div className="card mb-4 border-dashed w-[300px] h-[360px] border-2 border-gray-300 rounded items-center justify-center flex flex-col pb-2">
                        <div className="card-title uppercase text-[#4F2683] text-sm mb-2">
                            {t('print_modal.front')}
                        </div>
                        <img
                            className="avatar mx-auto max-h-60 max-w-[80px] rounded-full mb-2"
                            src={credential.image}
                            alt={credential.name}
                        />
                        <div className="name-text">{credential.name || t('print_modal.default_name')}</div>
                        <div className="visitor-p">{t('print_modal.visitor')}</div>
                        <div className="expire-text">{t('print_modal.expire')}: 2-04-2022 10:00 AM</div>
                        <img className="barcode" src={barcode} alt="Barcode" />
                    </div>

                    {/* Back Side Card Preview */}
                    <div className="card w-[300px] h-[360px] border-dashed border-2 border-gray-300 rounded items-center justify-center flex flex-col pb-2 px-10">
                        <div className="card-title uppercase text-[#4F2683] text-sm mb-2">
                            {t('print_modal.back')}
                        </div>
                        <div className="name-text">{credential.name || t('print_modal.default_name')}</div>
                        <div className="expire-text">{t('print_modal.expire')}: 2-04-2022 10:00 AM</div>
                        <div className="text-xs text-gray-600 mb-2">
                            {t('print_modal.info1')}
                        </div>
                        <div className="text-xs text-gray-600 mb-2">
                            {t('print_modal.info2')}
                        </div>
                        <img className="barcode" src={barcode} alt="Barcode" />
                    </div>
                </div>

                {/* Buttons */}
                <div className="flex justify-center gap-4 mt-4">
                    <button
                        onClick={handlePrint}
                        className="bg-[#4F2683] text-white px-6 py-2 rounded"
                    >
                        {t('print_modal.print')}
                    </button>
                    <button
                        onClick={onClose}
                        className="bg-gray-500 text-white px-4 py-2 rounded"
                    >
                        {t('print_modal.cancel')}
                    </button>
                </div>
            </div>
        </div>
    );
};

export default PrintModalBothSide;
