import React, { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import Facility from "../../Components/Facility/FacilityEdit.jsx";
import Building from "../../Components/Facility/Building/Building";
import Floor from "../../Components/Facility/Floor/Floor";
import Room from "../../Components/Facility/Room/Room";
import AccessAreas from "../../Components/Facility/AccessArea/AccessAreas";
import Device from "../../Components/PatientHub/Device";
import DeviceGroup from "../../Components/PatientHub/DeviceGroup";
import DetailsCard from "../../Components/Global/DetailsCard";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import Loader from "../../Components/Loader.jsx";
import userImg from "../../Images/Building.svg";
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from "../../Components/Observation/HistoryTable";
import { getFacilityById } from "../../api/facility";

const WatchListDetails = ({ tab = null }) => {
  const { facilityId } = useParams();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState(tab || t('facility.tab_facility'));
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [facilityData, setFacilityData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const tabKeys = [
    'facility.tab_facility',
    'facility.tab_building',
    'facility.tab_floor',
    'facility.tab_room',
    'facility.tab_access_areas',
    'Devices',
    'Device Group'
  ];

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  // Function to handle image capture
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  const fetchFacilityData = async () => {
    try {
      const response = await getFacilityById(facilityId);
      if (response && response.status) {
        console.log(response.data);
        setFacilityData(response.data);
      }
    } catch (error) {
      console.error("Error fetching facility data:", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    if (!facilityId) {
      console.error("Facility ID is missing");
      setLoading(false);
      return;
    }
    fetchFacilityData();
  }, [facilityId]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader />
      </div>
    );
  }
  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate("/facility")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('facility.title')}</h2>
        </div>
      </div>
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        handleHistoryOpen={handleHistoryOpen}
        profileImage={profileImage}
        defaultImage={userImg}
        name={facilityData?.name || "N/A"}
        showHistoryButton={true}
        additionalFields={[
          {
            label: t('facility.address'),
            value: facilityData?.address?.address_line_1 || "N/A",
          },
          {
            label: t('facility.status'),
            value: facilityData?.facility_status_name?.value || "N/A",
          },
          {
            label: t('facility.type'),
            value: facilityData?.facility_type_name?.value || "N/A",
          },
        ]}
      />

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-6">
          {tabKeys.map((tabKey) => (
            <button
              key={tabKey}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === t(tabKey) || (tabKey === 'Devices' && selectedTab === 'Devices') || (tabKey === 'Device Group' && selectedTab === 'Device Group') ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey))}
            >
              {tabKey === 'Devices' || tabKey === 'Device Group' ? tabKey : t(tabKey)}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] pt-4">
          {selectedTab === t('facility.tab_facility') && (
            <Facility
              facility={facilityData}
              refreshFacilityData={fetchFacilityData}
            />
          )}
          {selectedTab === t('facility.tab_building') && <Building facility={facilityData} />}
          {selectedTab === t('facility.tab_floor') && <Floor facility={facilityData} />}
          {selectedTab === t('facility.tab_room') && <Room />}
          {selectedTab === t('facility.tab_access_areas') && <AccessAreas />}
          {selectedTab === 'Devices' && <Device />}
          {selectedTab === 'Device Group' && <DeviceGroup />}
        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
      />
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default WatchListDetails;
