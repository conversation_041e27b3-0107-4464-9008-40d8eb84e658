import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import AddAccessRequest from "./AddAccessRequest"; // Import the new component
import viewicon from "../../../../Images/ViewIcon.svg";
import report from "../../../../Images/ReportList.svg";
import ViewAccess from "./ViewAccess"; // Import the new component
import ViewReport from "./ViewReport"; // Import the new component

const Access = () => {
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false); // State for modal visibility
  const [isViewModalOpen, setIsViewModalOpen] = useState(false); // State for ViewAccess modal
  const [isReportModalOpen, setIsReportModalOpen] = useState(false); // State for ViewReport modal

  const [accessData, setAccessData] = useState([
    {
      areaName: "BANGALORE, IND [BLR] GX GENERAL ACCESS 24HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "BERLIN, GER [BER10] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DEL - SANTA CLARA CA USA [SCA08] - SECURITY OFFICE AREA",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DUSSELDORF, GER [DUS02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "HAMBURG, GER [HAM02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "LIMA, PER [LIM01] - GENERAL ACCESS 24 HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
  ]);

  const handleAddAccess = (newAccess) => {
    setAccessData((prevData) => [...prevData, newAccess]);
  };

  const columns = [
    {
      name: "Area Name",
      selector: (row) => row.areaName,
      sortable: true,
    },
    {
      name: "Type",
      selector: (row) => row.type,
    },
    {
      name: "Start Date",
      selector: (row) => row.startDate,
    },
    {
      name: "End Date",
      selector: (row) => row.endDate,
    },
    {
      name: "Status",
      selector: (row) => row.status,
    },
    {
      name: "Action",
      cell: () => (
        <div className="flex justify-center items-center space-x-2">
          <img
            src={viewicon}
            alt=""
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => setIsViewModalOpen(true)} // Open ViewAccess modal
          />
          <img
            src={report}
            alt=""
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => setIsReportModalOpen(true)} // Open ViewReport modal
          />
        </div>
      ),
    },
  ];

  const filteredData = useMemo(() => {
    if (!tableSearchTerm) return accessData;
    return accessData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(tableSearchTerm.toLowerCase())
      )
    );
  }, [accessData, tableSearchTerm]);

  return (
    <div>
      <div className="bg-white rounded-lg shadow-md">
        
        <GenericTable
          title={"Access"}
          onAdd={() => setIsAddModalOpen(true)} // Open modal on click
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
      {isAddModalOpen && (
        <AddAccessRequest
          onClose={() => setIsAddModalOpen(false)}
          onAddAccess={handleAddAccess} // Pass the callback function
        />
      )}
      {isViewModalOpen && <ViewAccess onClose={() => setIsViewModalOpen(false)} />}
      {isReportModalOpen && <ViewReport onClose={() => setIsReportModalOpen(false)} />}
    </div>
  );
};

export default Access;
