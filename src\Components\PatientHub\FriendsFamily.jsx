import React, { useState, useEffect } from 'react';
import GenericTable from "../GenericTable";
import Swal from 'sweetalert2';
import AddFriendsForm from './FriendsAndFamily/AddFriendsAndFamily';
import ViewEditModal from './FriendsAndFamily/ViewEditModal';
import PatientHistoryModal from './PatientHistoryModal';
import {
  getPatientFriends,
  addPatientGuest,
  getPatientHistory,
  deletePatientGuest,
  updatePatientFriend
} from '../../api/PatientHub';
import Delete from "../../Images/Delete.svg";
import { PiClockCountdown } from "react-icons/pi";
import { toast } from 'react-toastify';
import { usePatientGuestTypeMasterData } from '../../hooks/usePatientGuestTypeMasterData';

const RELATIONSHIP_LABELS = {
  0: 'Spouse',
  1: 'Child',
  2: 'Friend',
  3: 'Emergency Contact',
};

const FriendsFamily = ({ patientId }) => {
  const [data, setData] = useState([]);
  const [filteredData, setFilteredData] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState('first_name');
  const [sortOrder, setSortOrder] = useState('ASC');
  const [selectedRow, setSelectedRow] = useState(null);
  const [isViewModalOpen, setViewModalOpen] = useState(false);
  const [isAddModalOpen, setAddModalOpen] = useState(false);
  const [isHistoryModalOpen, setHistoryModalOpen] = useState(false);
  const [isHistoryLoading, setHistoryLoading] = useState(false);
  const [historyData, setHistoryData] = useState([]);
  const [newEntry, setNewEntry] = useState({
    name: '',
    lastName: '',
    relationship: '', // will store numeric key 0–3
    email: '',
    phone: '',
  });

  const { relationshipTypes } = usePatientGuestTypeMasterData();

  useEffect(() => {
    const fetchFriends = async () => {
      try {
        const response = await getPatientFriends({
          patient_id: patientId,
          guest_type: 1,
          sortBy,
          sortOrder,
          search: searchQuery,
        });

        console.log("API Response:", response);

        const formattedData = response.map((friend) => {
          console.log(
            `Friend ${friend.patient_guest_id} relationship_type:`,
            friend.relationship_type
          );

          const relKey = friend.relationship_type;
          const relLabel = RELATIONSHIP_LABELS.hasOwnProperty(relKey)
            ? RELATIONSHIP_LABELS[relKey]
            : 'N/A';

          return {
            id: friend.patient_guest_id,
            name: `${friend.first_name} ${friend.last_name}`,
            lastName: friend.last_name || "",
            email: friend.email,
            phone: friend.phone,
            relationship: relLabel,
            relationshipKey: relKey,
          };
        });

        setData(formattedData);
        setFilteredData(formattedData);
      } catch (error) {
        console.error("Error fetching friends:", error);
      }
    };

    if (patientId) fetchFriends();
  }, [patientId, sortBy, sortOrder, searchQuery]);

  const handleRowClick = (row) => {
    setSelectedRow(row);
    setViewModalOpen(true);
  };

  const handleSearch = (e) => {
    setSearchQuery(e.target.value);
  };

  const handleSortClick = (column, sortDirection) => {
    setSortBy(column.id);
    setSortOrder(sortDirection.toUpperCase());
  };

  const handleUpdate = (updatedEntry) => {
    // Recombine for display
    const displayEntry = {
      ...updatedEntry,
      name: `${updatedEntry.name} ${updatedEntry.lastName}`.trim(),
    };
    setData((prev) =>
      prev.map((item) => (item.id === updatedEntry.id ? displayEntry : item))
    );
    setSelectedRow(displayEntry);
    setViewModalOpen(false);
  };

  const handleAddClick = () => {
    setNewEntry({
      name: '',
      lastName: '',
      relationship: '',
      email: '',
      phone: '',
    });
    setAddModalOpen(true);
  };

  const handleSave = async () => {
    // Ensure relationship_type is a number
    const payload = {
      first_name: newEntry.name || "",
      last_name: newEntry.lastName || "",
      email: newEntry.email,
      phone: newEntry.phone,
      guest_type: 1,
      birth_date: "1990-01-01",
      reason: "N/A",
      denied_on: new Date().toISOString(),
      patient_id: patientId,
      relationship_type: parseInt(newEntry.relationship, 10), // Explicitly convert to number
    };

    console.log("→ Final payload:", payload);

    try {
      const response = await addPatientGuest(payload, 1);
      toast.success("Guest added successfully!", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });

      // Re-fetch the updated list:
      const updatedFriends = await getPatientFriends({
        patient_id: patientId,
        guest_type: 1,
      });

      const formattedData = updatedFriends.map((friend) => {
        const relKey = friend.relationship_type;
        const relLabel = RELATIONSHIP_LABELS.hasOwnProperty(relKey)
          ? RELATIONSHIP_LABELS[relKey]
          : 'N/A';

        return {
          id: friend.patient_guest_id,
          name: `${friend.first_name} ${friend.last_name}`,
          lastName: friend.last_name || "",
          email: friend.email,
          phone: friend.phone,
          relationship: relLabel,
          relationshipKey: relKey,
        };
      });

      setData(formattedData);
      setFilteredData(formattedData);
      setAddModalOpen(false);
    } catch (error) {
      console.error("Error adding guest:", error);
      toast.error("Failed to add the guest. Please try again.", {
        position: "top-right",
        autoClose: 3000,
        hideProgressBar: true,
        closeOnClick: true,
        pauseOnHover: true,
        draggable: true,
        progress: undefined,
        theme: "light",
      });
    }
  };

  const handleDelete = async (id) => {
    Swal.fire({
      title: "Are you sure?",
      text: "Do you really want to delete this record?",
      icon: "warning",
      showCancelButton: true,
      confirmButtonText: "Yes, delete it!",
      cancelButtonText: "Cancel",
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          await deletePatientGuest(id);
          setData((prev) => prev.filter((item) => item.id !== id));
          toast.success("Record deleted successfully!", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
          });
        } catch (error) {
          console.error("Error deleting guest:", error);
          toast.error("Failed to delete the record. Please try again.", {
            position: "top-right",
            autoClose: 3000,
            hideProgressBar: true,
            closeOnClick: true,
            pauseOnHover: true,
            draggable: true,
            progress: undefined,
            theme: "light",
          });
        }
      }
    });
  };

  const handleHistoryClick = async () => {
    setHistoryLoading(true);
    try {
      const response = await getPatientHistory({ patient_id: patientId });
      console.log("Patient History Response:", response);
      setHistoryData(response.data);
      setHistoryModalOpen(true);
    } catch (error) {
      console.error("Error fetching patient history:", error);
    } finally {
      setHistoryLoading(false);
    }
  };

  const columns = [
    {
      id: 'first_name',
      name: 'Name',
      selector: (row) => row.name,
      sortable: true,
      cell: (row) => (
        <span className="underline cursor-pointer" onClick={() => handleRowClick(row)}>
          {row.name}
        </span>
      ),
    },
    {
      id: 'relationship_type',
      name: 'Relationship',
      selector: (row) => row.relationship,
      sortable: true,
    },
    {
      id: 'email',
      name: 'Email',
      selector: (row) => row.email,
      sortable: true,
    },
    {
      id: 'phone',
      name: 'Phone',
      selector: (row) => row.phone,
      sortable: true,
    },
    {
      name: 'Action',
      cell: (row) => (
        <div className="flex items-center gap-2">
          <img
            src={Delete}
            alt="Delete"
            className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={() => handleDelete(row.id)}
          />
          <PiClockCountdown
            className="font-[500] text-xl p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer"
            onClick={handleHistoryClick}
          />
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="relative">
      {isHistoryLoading && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
          <div className="relative w-16 h-16">
            <div className="absolute inset-0 rounded-full bg-[#4F2683] opacity-75 animate-ping" />
            <div className="relative rounded-full h-full w-full border-4 border-[#4F2683]" />
          </div>
        </div>
      )}

      <div className="bg-white rounded-[10px]">
        <GenericTable
          title="Friends & Family"
          searchTerm={searchQuery}
          onSearchChange={handleSearch}
          onSort={handleSortClick}
          columns={columns}
          data={filteredData}
          showSearch={true}
          showAddButton={true}
          onAdd={handleAddClick}
        />
      </div>

      {isViewModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditModal
                familyData={selectedRow}
                onUpdate={handleUpdate}
                onClose={() => setViewModalOpen(false)}
              />
            </div>
          </div>
        </div>
      )}

      {isAddModalOpen && (
        <AddFriendsForm
          newEntry={newEntry}
          setNewEntry={setNewEntry}
          onSave={handleSave}
          onClose={() => setAddModalOpen(false)}
        />
      )}

      {isHistoryModalOpen && (
        <PatientHistoryModal
          onClose={() => setHistoryModalOpen(false)}
          historyData={historyData}
        />
      )}
    </div>
  );
};

export default FriendsFamily;
