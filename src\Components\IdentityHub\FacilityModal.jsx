import React, { useState } from "react";
import Input from "../Global/Input/Input";
import Button from "../Global/Button";
import ImageCapture from "../Global/ImageAndCamera/ImageCaptureForForm";
import CustomDropdown from "../Global/CustomDropdown";

const IdentityModal = ({ onClose, onAddFacility }) => {
  const [formData, setFormData] = useState({
    image: "",
    facility: "",
    status: "",
    facilityCode: "",
    facilityType: "", // This will be set via the CustomDropdown
    timeZone: "",
    facilityPhone: "",
    facilityEmail: "",
    geoLocationCode: "",
    otherCode: "",
    facilityUrl: "",
    connectedApplications: "",
    facilityNotes: "",
    // ADDRESS SECTION 
    address1: "",
    address2: "",
    country: "",
    state: "",
    postalCode: "",
    mapUrl: "",
    region: "",
  });

  // Handler for traditional input changes
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  // Handlers for image capture/upload
  const handleImageCaptured = (imageSrc) => {
    setFormData((prevData) => ({ ...prevData, image: imageSrc }));
  };

  const handleImageUploaded = (uploadedImage) => {
    console.log("Uploaded Image:", uploadedImage);
    setFormData((prevData) => ({ ...prevData, image: uploadedImage }));
  };

  // Form submission handler
  const handleSubmit = (e) => {
    e.preventDefault();
    onAddFacility(formData);
    onClose();
  };

  // Facility fields configuration
  const facilityFields = [
    { label: "Facility Name *", type: "text", placeholder: "Facility Name", name: "facility" },
    { 
      label: "Status", 
      type: "customDropdown", 
      placeholder: "Select status",
      name: "status", 
      options: ["Active", "Expired"] 
    },
    { label: "Facility Code", type: "text", placeholder: "Facility Code", name: "facilityCode" },
    { 
      label: "Facility Type", 
      type: "customDropdown",
      placeholder: "Facility Type",
      name: "facilityType",
      options: ["Facility 1", "Facility 2", "Facility 3"]
    },
    { label: "Time Zone", type: "text", placeholder: "Time Zone", name: "timeZone" },
    { label: "Facility Phone", type: "tel", placeholder: "Facility Phone", name: "facilityPhone" },
    { label: "Facility Email", type: "email", placeholder: "Facility Email", name: "facilityEmail" },
    { label: "Geo Location Code", type: "text", placeholder: "Geo Location Code", name: "geoLocationCode" },
    { label: "Other Code", type: "text", placeholder: "Other Code", name: "otherCode" },
    { label: "Facility Url", type: "text", placeholder: "Facility Url", name: "facilityUrl" },
    { label: "Connected Applications", type: "text", placeholder: "Managed By System(s)", name: "connectedApplications" },
    { label: "Facility Notes", type: "text", placeholder: "Facility Notes", name: "facilityNotes" },
  ];

  // Address fields configuration
  const addressFields = [
    { label: "Address 1", type: "text", placeholder: "Address", name: "address1" },
    { label: "Address 2", type: "text", placeholder: "Address", name: "address2" },
    { label: "Country", 
      type: "customDropdown",
      placeholder: "Country", name: "country",
      options: ["India", "USA", "Nepal", "Bhutan","France","Czech Republic",]
     },
    { label: "State / Province", 
      type: "customDropdown",
      placeholder: "State", name: "state",
      options: ["State 1", "State 2", "State 3", "State 4","State 5","State 6"]
     },

    // { label: "State / Province", type: "text", placeholder: "State/Province", name: "state" },
    { label: "Postal Code", type: "text", placeholder: "Postal Code", name: "postalCode" },
    { label: "Map Url", type: "text", placeholder: "Url....", name: "mapUrl" },
    { label: "Region", type: "text", placeholder: "Region", name: "region" },
  ];

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-6">
      
      <div className="bg-white w-full h-full max-w-5xl p-4 rounded-lg shadow-lg overflow-y-auto">
        <div className="flex justify-between items-center">
          <h2 className="text-[30px] text-[#4F2683] font-normal">Add Facility</h2>
          <button
            onClick={onClose}
            className="flex items-center justify-center bg-[#4F2683] text-white text-2xl rounded-full h-8 w-8 hover:bg-[#6A3BAA]"
          >
            &times;
          </button>
        </div>
        <hr className="mb-4 mt-2" />
        <div className="mb-16">
          <ImageCapture
            onImageCaptured={handleImageCaptured}
            onImageUploaded={handleImageUploaded}
          />
        </div>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            {/* Facility Details Section */}
            <h3 className="font-medium text-[20px] pb-4 text-[#333333]">Facility Details</h3>
            {facilityFields.map(({ label, type, name, options, placeholder }, idx) => (
              <div key={idx} className="flex mb-2">
                <label className="mr-2 w-1/3">{label}</label>
                {type === "customDropdown" ? (
                  <CustomDropdown
                    value={formData[name]}
                    options={options}
                    placeholder={placeholder}
                    onSelect={(selectedOption) =>
                      setFormData((prev) => ({ ...prev, [name]: selectedOption }))
                    }
                    // Optional styling props
                    bgColor="bg-[white] text-black"
                        textColor="text-black"
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                        className={"p-2 border h-11  rounded focus:outline-none focus:ring-1"}
                    rounded="rounded"
                  />
                ) : (
                  <Input
                    type={type}
                    name={name}
                    value={formData[name]}
                    placeholder={placeholder}
                    onChange={handleChange}
                    // Pass options for select type if needed
                    options={options}
                    required={label === "Facility Name *"}
                  />
                )}
              </div>
            ))}
            {/* Address Information Section */}
            <h3 className="font-medium text-[20px] py-4  text-[#333333]">Address Information</h3>
            {addressFields.map(({ label, type, name, placeholder,options }, idx) => (
              <div key={idx} className="flex mb-2">
                <label className="mr-2 w-1/3">{label}</label>
                {type === "customDropdown" ? (
                  <CustomDropdown
                    value={formData[name]}
                    options={options}
                    placeholder={placeholder}
                    onSelect={(selectedOption) =>
                      setFormData((prev) => ({ ...prev, [name]: selectedOption }))
                    }
                    // Optional styling props
                    bgColor="bg-[white] text-black"
                        textColor="text-black"
                        hoverBgColor="hover:bg-[#4F2683]"
                        borderColor="border-gray-300"
                        className={"p-2 border h-11  rounded focus:outline-none focus:ring-1"}
                    rounded="rounded"
                  />
                ) : (
                  <Input
                    type={type}
                    name={name}
                    value={formData[name]}
                    placeholder={placeholder}
                    onChange={handleChange}
                    // Pass options for select type if needed
                    options={options}
                    required={label === "Facility Name *"}
                  />
                )}
              </div>
            ))}
            <div className="flex justify-center gap-4 mt-6">
              <Button type="cancel" label="Cancel" onClick={onClose} />
              <Button type="primary" label="Add" />
            </div>
          </div>
        </form>
      </div>

    </div>
  );
};

export default IdentityModal;
