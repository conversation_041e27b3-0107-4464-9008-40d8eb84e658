import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddCardForm from "./AddCardForm";
import ViewEditCardForm from "./ViewEditCardForm";
import deleted from "../../../Images/Delete.svg";
import TruncatedCell from "../../Tooltip/TruncatedCell";
import TruncatedRow from "../../Tooltip/TrucantedRow";
import {
  viewCard,
  deleteCard,
  viewCardByIdentity,
} from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom"; // Import useLocation
import { useCardData } from "../../../hooks/useCardData";

const Card = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(false); // Loading state
  const [showAddCardForm, setShowAddCardForm] = useState(false);
  const [showViewCardForm, setShowViewCardForm] = useState(false);
  const [selectedCard, setSelectedCard] = useState(null);
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id"); // Fetch identity_id from query parameters
  const { statusMap, templateMap, formatMap } = useCardData();

  const fetchCardData = useCallback(async () => {
    setLoading(true);
    try {
        const params = { 
        search: searchTerm || undefined,
      };
      const res = await viewCardByIdentity(identityId ,params);
      const cardData = Array.isArray(res.data) ? res.data : [res.data]; // handle both array and object
      setCards(cardData);
    } catch (error) {
      toast.error("Error fetching card data.");
      console.error("Error fetching card data:", error);
    } finally {
      setLoading(false);
    }
  }, [identityId , searchTerm]);

  useEffect(() => {
    fetchCardData(); // Call the function to fetch data
  }, [fetchCardData]);

  const handleAdd = () => {
    setShowAddCardForm(true);
  };

  const handleDelete = async (card) => {
    try {
      await deleteCard(card.card_id); // Call the API to delete the card
      setCards((prevCards) =>
        prevCards.filter((c) => c.card_id !== card.card_id)
      ); // Update state
      toast.success("Card deleted successfully!");
    } catch (error) {
      toast.error("Failed to delete card. Please try again.");
      console.error("Error deleting card:", error);
    }
  };

  const handleView = (card) => {
    setSelectedCard(card);
    setShowViewCardForm(true);
  };

  const handleUpdate = (updatedCard) => {
    setCards((prevCards) =>
      prevCards.map((card) =>
        card.card_id === updatedCard.card_id
          ? { ...card, ...updatedCard }
          : card
      )
    );
    setShowViewCardForm(false);
  };

  const columns = [
    {
      name: <TruncatedCell text="Card Number" />,
      selector: (row) => row.card_number,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.card_number}
        </span>
      ),
      sortable: true,
    },
    {
      name: <TruncatedCell text="Card Format" />,
      selector: (row) => row.card_format,
      cell: (row) => (
        <TruncatedRow
          text={
            formatMap
              ? formatMap[row.card_format] || row.card_format
              : row.card_format
          }
        />
      ),
    },
    {
      name: <TruncatedCell text="Facility Code" />,
      selector: (row) => row.facility_code,
    },
    {
      name: "Pin",
      selector: (row) => row.pin,
    },
    {
      name: <TruncatedCell text="Template" />,
      selector: (row) => row.template,
      cell: (row) => (
        <TruncatedRow
          text={
            templateMap
              ? templateMap[row.template] || row.template
              : row.template
          }
        />
      ),
    },
    {
      name: <TruncatedCell text="Activation Date" />,
      selector: (row) => row.active_date,
    },
    {
      name: <TruncatedCell text="Deactivation Date" />,
      selector: (row) => row.deactive_date,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status === "0"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {statusMap ? statusMap[row.status] || row.status : row.status}
        </span>
      ),
    },
    {
      name: "Reason",
      selector: (row) => row.reason,
      cell: (row) => <TruncatedCell text={row.reason} />,
    },
    {
      name: "Action",
      cell: (row) => (
        <img
          src={deleted}
          alt="deleted"
          className="p-2 rounded-lg cursor-pointer bg-[#E21B1B14]"
          onClick={() => handleDelete(row)} // Call handleDelete on click
        />
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Cards"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        onAdd={handleAdd}
        data={cards}
        showSearch={true}
        showAddButton={true}
      />

      {showAddCardForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddCardForm
                onClose={() => setShowAddCardForm(false)}
                onSubmit={(newCard) => {
                  setCards((prev) => [
                    { ...newCard, card_id: Date.now() },
                    ...prev,
                  ]); // Temporary ID until refresh
                  fetchCardData(); // Refresh data from server
                  setShowAddCardForm(false);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {showViewCardForm && selectedCard && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditCardForm
                cardData={selectedCard}
                onClose={() => setShowViewCardForm(false)}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Card;
