import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import AddAccessRequest from "./AddAccessRequest"; 
import viewicon from "../../../../Images/ViewIcon.svg";
import report from "../../../../Images/ReportList.svg";
import ViewAccess from "./ViewAccess"; 
import ViewReport from "./ViewReport"; 
import TruncatedCell from "../../../Tooltip/TruncatedCell"; 
import TruncatedRow from "../../../Tooltip/TrucantedRow"; 
const Access = () => {
  const [tableSearchTerm, setTableSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false); 
  const [isViewModalOpen, setIsViewModalOpen] = useState(false); 
  const [isReportModalOpen, setIsReportModalOpen] = useState(false); 

  const [accessData, setAccessData] = useState([
    {
      areaName: "BANGALORE, IND [BLR] GX GENERAL ACCESS 24HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "BERLIN, GER [BER10] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DEL - SANTA CLARA CA USA [SCA08] - SECURITY OFFICE AREA",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "DUSSELDORF, GER [DUS02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "HAMBURG, GER [HAM02] - GENERAL ACCESS",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
    {
      areaName: "LIMA, PER [LIM01] - GENERAL ACCESS 24 HR",
      type: "Area",
      startDate: "19-Mar-2025 | 11:46 PM",
      endDate: "19-Mar-2025 | 11:46 PM",
      status: "Assigned",
    },
  ]);

  const handleAddAccess = (newAccess) => {
    setAccessData((prevData) => [...prevData, newAccess]);
  };

  const columns = [
    {
      name: <TruncatedCell text="Area Name" />,
      selector: (row) => row.areaName,
      cell: (row) => <TruncatedRow text={row.areaName} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Type" />,
      selector: (row) => row.type,
      cell: (row) => <TruncatedRow text={row.type} />,
    },
    {
      name: <TruncatedCell text="Start Date" />,
      selector: (row) => row.startDate,
      cell: (row) => <TruncatedRow text={row.startDate} />,
    },
    {
      name: <TruncatedCell text="End Date" />,
      selector: (row) => row.endDate,
      cell: (row) => <TruncatedRow text={row.endDate} />,
    },
    {
      name: <TruncatedCell text="Status" />,
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center rounded-full ${
            row.status.toLowerCase() === "assigned"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: <TruncatedCell text="Action" />,
      cell: () => (
        <div className="flex justify-center items-center space-x-2">
          <div className="relative group">
            <img
              src={viewicon}
              alt="View Icon"
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => setIsViewModalOpen(true)} // Open ViewAccess modal
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              View Access
            </div>
          </div>
          <div className="relative group">
            <img
              src={report}
              alt="Report Icon"
              className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
              onClick={() => setIsReportModalOpen(true)} // Open ViewReport modal
            />
            <div className="bottom-full left-1/2 -translate-x-1/2 mb-2 hidden group-hover:block text-white text-xs rounded px-2 py-1 z-10 whitespace-nowrap !text !p-1 !bg-[#3D156F] !bg-opacity-90 !absolute !font-medium">
              View Request
            </div>
          </div>
        </div>
      ),
    },
  ];

  const filteredData = useMemo(() => {
    if (!tableSearchTerm) return accessData;
    return accessData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(tableSearchTerm.toLowerCase())
      )
    );
  }, [accessData, tableSearchTerm]);

  return (
    <div>
      <div className="bg-white rounded-lg shadow-md">
        <GenericTable
          title={"Access"}
          onAdd={() => setIsAddModalOpen(true)} // Open modal on click
          searchTerm={tableSearchTerm}
          onSearchChange={(e) => setTableSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
      {isAddModalOpen && (
        <AddAccessRequest
          onClose={() => setIsAddModalOpen(false)}
          onAddAccess={handleAddAccess} // Pass the callback function
        />
      )}
      {isViewModalOpen && <ViewAccess onClose={() => setIsViewModalOpen(false)} />}
      {isReportModalOpen && <ViewReport onClose={() => setIsReportModalOpen(false)} />}
    </div>
  );
};

export default Access;