import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
// import LanguageDetector from 'i18next-browser-languagedetector'

// Import all JSON translations
import translationEN from './locales/en.json';
import translationES from './locales/es.json';

const resources = {
  en: { translation: translationEN },
  es: { translation: translationES }
};

i18n
//  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    debug:true,
    resources,
    lng: 'en',
    fallbackLng: 'en',
    interpolation: { escapeValue: false },
    detection: {
      order: ['querystring', 'cookie', 'localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'], // saves user's language preference
    },
    react: {
      useSuspense: false, // if you don't want loading fallback, disable Suspense
    },
  });

export default i18n;
