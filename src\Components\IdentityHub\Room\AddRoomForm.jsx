import React, { useState } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import Button from "../../Global/Button";
import Attachment from "../../../Images/Attachment.png"
import DateInput from "../../Global/Input/DateInput";
const AddDocumentForm = ({ onSubmit, onClose }) => {
  const [documentType, setDocumentType] = useState("");
  const [otherDocument, setOtherDocument] = useState("");
  const [documentNumber, setDocumentNumber] = useState("");
  const [status, setStatus] = useState("");
  const [issueDate, setIssueDate] = useState("");
  const [expirationDate, setExpirationDate] = useState("");
  const [country, setCountry] = useState("");
  const [state, setState] = useState("");
  const [otherIssuer, setOtherIssuer] = useState("");
  const [note, setNote] = useState("");
  const [attachment, setAttachment] = useState(null);

  // Inline file upload handler
  const handleFileChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      setAttachment(e.target.files[0]);
    }
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Basic validation for required fields
    if (
      !String(documentType).trim() ||
      (String(documentType) === "Other" && !String(otherDocument).trim()) ||
      !String(documentNumber).trim() ||
      !String(issueDate).trim() ||
      !String(expirationDate).trim()
    ) {
      alert("Please fill in all required fields.");
      return;
    }
    const uploadedDate = new Date().toISOString().split("T")[0];
    const newDocument = {
      documentName: String(documentType) === "Other" ? otherDocument : documentType,
      documentNumber,
      issueDate,
      expirationDate,
      country,
      state,
      uploadedDate,
      status,
      otherIssuer,
      note,
      attachment: attachment ? URL.createObjectURL(attachment) : "",
    };
    onSubmit(newDocument);

    // Reset fields after successful submission
    setDocumentType("");
    setOtherDocument("");
    setDocumentNumber("");
    setStatus("");
    setIssueDate("");
    setExpirationDate("");
    setCountry("");
    setState("");
    setOtherIssuer("");
    setNote("");
    setAttachment(null);
  };

  return (
    <div className="w-full p-4">
      <div className="flex items-center justify-between mb-2">
        <h2 className="text-[30px] font-normal text-[#4F2683]">Add Document</h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mb-4" />
      <form onSubmit={handleSubmit} className="bg-white p-6 rounded-lg">
        {/* Document Type */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Document Type*</label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={["Passport", "Driver License", "Other"]}
              onSelect={(option) => setDocumentType(option)}
              selectedOption={documentType}
              placeholder="Select Document Type"
            />
          </div>
        </div>
        {/* Specify Other Document */}
        {String(documentType) === "Other" && (
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal">Specify Document*</label>
            <div className="w-3/4">
              <Input
                type="text"
                placeholder="Specify Document"
                value={otherDocument}
                onChange={(e) => setOtherDocument(e.target.value)}
                className="w-full border border-gray-300 rounded p-2"
              />
            </div>
          </div>
        )}
        {/* Document Number */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Document Number*</label>
          <div className="w-3/4">
            <Input
              type="text"
              placeholder="Document Number"
              value={documentNumber}
              onChange={(e) => setDocumentNumber(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>
        {/* Status */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Status</label>
          <div className="w-3/4">
            <CustomDropdown
              className="h-11 rounded"
              options={["Active", "Inactive", "Expired"]}
              onSelect={(option) => setStatus(option)}
              selectedOption={status}
              placeholder="Set Status"
            />
          </div>
        </div>
        {/* Issue Date */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Issue Date*</label>
          <div className="w-3/4">
            <DateInput
                          name="endDate"
                          className=" w-full"
                          id="endDate"
                          value={issueDate}
                          onChange={(date) => setIssueDate(date ? date.toISOString().split("T")[0] : "")}
                          placeholder="MM-DD-YYYY"
                        />
          </div>
        </div>
        {/* Expiration Date */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Expiration Date*</label>
          <div className="w-3/4">
            <DateInput
            name="endDate"
            className=" w-full"
            id="endDate"
            value={expirationDate}
            onChange={(date) => setExpirationDate(date ? date.toISOString().split("T")[0] : "")}
            placeholder="MM-DD-YYYY"
          />
          </div>
        </div>
        {/* Country */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Country</label>
          <div className="w-3/4">
            <Input
              type="text"
              placeholder="Country"
              value={country}
              onChange={(e) => setCountry(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>
        {/* State */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">State</label>
          <div className="w-3/4">
            <Input
              type="text"
              placeholder="State"
              value={state}
              onChange={(e) => setState(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>
        {/* Other Issuer */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Other Issuer</label>
          <div className="w-3/4">
            <Input
              type="text"
              placeholder="Other Issuer"
              value={otherIssuer}
              onChange={(e) => setOtherIssuer(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            />
          </div>
        </div>
        {/* Note */}
        <div className="flex items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Note</label>
          <div className="w-3/4">
            <textarea
              placeholder="Note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              className="w-full border border-gray-300 rounded p-2"
            ></textarea>
          </div>
        </div>
        {/* Attachment (Inline File Upload) */}
        <div className="items-center mb-4">
          <label className="w-1/4 text-[16px] font-normal">Attachment</label>
          <div className="w-3/4">
            <label
              style={{
                display: "inline-block",
                cursor: "pointer",
                padding: "0.5rem",
                backgroundColor: "#E5E7EB",
                borderRadius: "0.375rem",
              }}
            >
              <img src={Attachment} alt="Attachment" />
              <input type="file" onChange={handleFileChange} style={{ display: "none" }} />
            </label>
            {attachment && <p className="mt-2 text-gray-700">{attachment.name}</p>}
          </div>
        </div>
        {/* Form Buttons */}
        <div className="flex gap-4 justify-end mt-6">
          <Button type="cancel" label="Cancel" onClick={onClose} />
          <button type="submit" className="px-4 py-2 bg-[#4F2683] text-white rounded">
            Add
          </button>
        </div>
      </form>
    </div>
  );
};

export default AddDocumentForm;
