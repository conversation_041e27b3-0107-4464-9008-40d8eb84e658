import React, { useState } from "react";
import GenericTable from "../GenericTable";
import Button from "./Button";

const Alert = ({ isOpen, data, screeningId, onClose, onSubmit }) => {
  const [reason, setReason] = useState("");

  if (!isOpen) return null;

  const columns = [
    { name: "First Name", selector: (row) => row.first_name, sortable: true },
    { name: "Last Name", selector: (row) => row.last_name, sortable: true },
    { name: "DOB", selector: (row) => row.dob, sortable: true },
    { name: "Gender", selector: (row) => row.gender, sortable: true },
    { name: "Hair Color", selector: (row) => row.hair_color, sortable: true },
    { name: "Added By", selector: (row) => row.added_by, sortable: true },
    { name: "Match Type", selector: (row) => row.match_type, sortable: true },
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!reason.trim()) {
      console.error("❌ Reason is required.");
      return;
    }
    if (typeof onSubmit === "function") {
      onSubmit(screeningId, reason); // Call the parent-provided function
    } else {
      console.error("❌ onSubmit is not a function.");
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg shadow-lg w-3/4">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold">Screening Alert</h2>
          <button
            className="w-8 h-8 bg-[#4F2683] text-2xl text-white rounded-full"
            onClick={onClose}
            type="button"
          >&times;</button>
        </div>

        <GenericTable
          title="Screening Details"
          columns={columns}
          data={data}
          fixedHeader
          fixedHeaderScrollHeight="300px"
          highlightOnHover
          striped
          showAddButton={false}
        />

        <form onSubmit={handleSubmit} className="mt-4">
          <label htmlFor="reason" className="block text-sm font-medium text-gray-700 mb-2">
            Reason
          </label>
          <textarea
            id="reason"
            name="reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            className="w-full border border-gray-300 rounded-lg p-2 mb-4"
            rows="4"
            placeholder="Enter your reason here…"
          />

          <button
            type="submit"
            className="bg-[#4F2683] text-white px-4 py-2 rounded-lg hover:bg-[#3b1d66]"
          >
            Override   
          </button>
        </form>
      </div>
    </div>
  );
};

export default Alert;
