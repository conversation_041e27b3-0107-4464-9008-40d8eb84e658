{"name": "reception-desk", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^7.28.3", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.8.1", "browser-image-compression": "^2.0.2", "cra-template": "1.2.0", "date-fns": "^2.28.0", "dayjs": "^1.11.13", "formik": "^2.4.6", "html2canvas": "^1.4.1", "i18next": "^25.2.1", "jspdf": "^3.0.1", "konva": "^9.3.22", "moment": "^2.30.1", "mustache": "^4.2.0", "primeicons": "^7.0.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-clock": "^5.1.0", "react-data-table-component": "^7.6.2", "react-datepicker": "^8.0.0", "react-datetime": "^3.3.1", "react-datetime-picker": "^6.0.1", "react-dom": "^19.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.57.0", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-infinite-scroll-component": "^6.1.0", "react-konva": "^19.0.7", "react-modal": "^3.16.3", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5", "react-scripts": "5.0.1", "react-signature-canvas": "^1.0.7", "react-time-picker": "^7.0.0", "react-toastify": "^11.0.3", "react-tooltip": "^5.28.0", "react-webcam": "^7.2.0", "redux-thunk": "^3.1.0", "styled-components": "^6.1.18", "sweetalert2": "^11.16.0", "sweetalert2-react-content": "^5.1.0", "uuid": "^11.1.0", "web-vitals": "^4.2.4", "yup": "^1.6.1", "zustand": "^5.0.6"}, "scripts": {"start": "set PORT=3005 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "tailwindcss": "^3.4.17"}, "jest": {"transformIgnorePatterns": ["node_modules/(?!(axios)/)"]}}