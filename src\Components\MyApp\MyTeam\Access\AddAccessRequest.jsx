import React, { useState } from "react";
import GenericTable from "../../../GenericTable"; // Import GenericTable
import CustomDropdown from "../../../Global/CustomDropdown"
import Input from "../../../Global/Input/Input";

const AddAccessRequest = ({ onClose, onAddAccess }) => {
  const [formData, setFormData] = useState({
    startDate: "",
    duration: "",
    endDate: "",
    justification: "",
    accessAreas: [],
  });

  const accessAreasData = [
    { areaName: "Area Role A", type: "Area", approvalRequested: "Yes" },
    { areaName: "Area Role B", type: "Area", approvalRequested: "No" },
  ];

  const accessAreasColumns = [
    { name: "Area Name", selector: (row) => row.areaName, sortable: true },
    { name: "Type", selector: (row) => row.type },
    { name: "Approval Requested", selector: (row) => row.approvalRequested },
  ];

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form Data Submitted:", formData);
    onAddAccess({
      areaName: "New Area", // Replace with actual area name if needed
      type: "Area", // Replace with actual type if needed
      startDate: formData.startDate,
      endDate: formData.endDate,
      status: "Pending", // Default status for new requests
    });
    onClose(); // Close modal after submission
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg h-full overflow-y-auto">
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Add Access Request
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Access Details
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Access Start Date
            </label>
            <div className="w-3/4">
              <input
                type="datetime-local"
                name="startDate"
                value={formData.startDate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Duration
  </label>
  <div className="w-3/4">
    <CustomDropdown
      options={[
        { label: "1 Hour", value: "1 Hour" },
        { label: "1 Day", value: "1 Day" },
        { label: "1 Week", value: "1 Week" },
      ]}
      value={formData.duration}
      onSelect={(value) => handleChange({ target: { name: "duration", value } })}
      placeholder="Select Duration"
      className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
    />
  </div>
</div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Access End Date
            </label>
            <div className="w-3/4">
              <input
                type="datetime-local"
                name="endDate"
                value={formData.endDate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="flex mb-2 items-center">
            <label className="w-1/4">Justification</label>
            <div className="w-3/4">
              <Input
                name="justification"
                type="bubbles"
                placeholder="Justification"
                value={formData.justification}
                height="94px"
                bubbles={true}
                bubbleOptions={[
                  "Lost Permanent Card",
                  "Forgot Permanent Card",
                ]}
                onChange={handleChange}
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
            Select & Add Access Area(s) *
            </label>
            <div className="w-3/4">
              <input
                type="text"
                placeholder="Enter Access End Date"
                name="endDate"
                
                
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
              />
            </div>
          </div>
          <div className="mb-4">
            <h2 className="text-[20px] text-[#333333] font-medium pb-4">
              Select & Add Access Area(s)
            </h2>
            <GenericTable
              showAddButton={false}
              columns={accessAreasColumns}
              data={accessAreasData}
              fixedHeader
              fixedHeaderScrollHeight="200px"
              highlightOnHover
              striped
            />
          </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddAccessRequest;
