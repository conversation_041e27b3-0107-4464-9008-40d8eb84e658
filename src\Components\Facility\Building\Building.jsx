import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import AddBuildingForm from "./AddBuildingForm";
import ViewEditBuildingForm from "./ViewEditBuildingForm";
import { getBuildingsByFacility } from "../../../api/facility";
import Loader from "../../Loader.jsx";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

const Building = ({ facility }) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddBuildingForm, setShowAddBuildingForm] = useState(false);
  const [showViewBuildingForm, setShowViewBuildingForm] = useState(false);
  const [selectedBuilding, setSelectedBuilding] = useState(null);

  useEffect(() => {
    if (!facility) {
      console.error("facilityId is undefined. Check your route configuration.");
      setLoading(false);
    }
  }, [facility]);

  const fetchBuildings = useCallback(async () => {
    if (!facility) return;
    try {
      const response = await getBuildingsByFacility(facility.facility_id);
      // Assuming the building array is inside response.data.data.
      setData(response.data.data);
    } catch (error) {
      console.error("Error fetching buildings:", error);
      toast.error("Failed to load buildings.");
    } finally {
      setLoading(false);
    }
  }, [facility]);

  useEffect(() => {
    if (facility.facility_id) {
      fetchBuildings();
    }
  }, [facility, fetchBuildings]);

  const handleAdd = () => {
    setShowAddBuildingForm(true);
  };

  const handleCloseModal = () => {
    setShowAddBuildingForm(false);
  };

  // Ensure the selected building always has an id
  const handleView = (row) => {
    const buildingWithId = { ...row, id: row.id || row.building_id };
    setSelectedBuilding(buildingWithId);
    setShowViewBuildingForm(true);
  };

  const handleCloseViewModal = () => {
    setShowViewBuildingForm(false);
  };

  const columns = [
    {
      name: "Building",
      selector: (row) => row.name,
      cell: (row) => (
        <span
          style={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={() => handleView(row)}
        >
          {row.name}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Building Code",
      selector: (row) => row.building_code,
    },
    {
      name: "Status",
      selector: (row) =>
        row.building_status_name ? row.building_status_name.value : row.status,
      cell: (row) => {
        const statusText = row.building_status_name
          ? row.building_status_name.value
          : row.status;
        return (
          <span
            className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
              statusText.toLowerCase() === "active"
                ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
          >
            {statusText}
          </span>
        );
      },
      center: true,
    },
    {
      name: "Type",
      selector: (row) =>
        row.building_type_name ? row.building_type_name.value : row.type,
    },
    {
      name: "Occupancy",
      selector: (row) =>
        row.building_occupancy_type_name
          ? row.building_occupancy_type_name.value
          : row.occupancy_type,
    },
    {
      name: "Phone",
      selector: (row) => row.phone,
    },
    {
      name: "Email",
      selector: (row) => row.email,
    },
    {
      name: "Facility",
      selector: (row) =>
        row.facility ? row.facility.name : row.facility_name || row.facility_id,
    },
  ];
  
  return (
    <div className="bg-white rounded-[10px]">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title="Buildings"
          searchTerm={searchTerm}
          showSearch={true}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={handleAdd}
          columns={columns}
          data={data}
          showAddButton={true}
        />
      )}
      {showAddBuildingForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]"> */}
            {/* <div className="rounded-lg max-h-[90vh] overflow-y-auto relative"> */}
              <AddBuildingForm
                facility={facility}
                onClose={handleCloseModal}
                
                fetchBuildings={fetchBuildings}
              />
            {/* </div> */}
          {/* </div> */}
        </div>
      )}
      {showViewBuildingForm && selectedBuilding && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              
              <ViewEditBuildingForm
                onClose={handleCloseViewModal}
                buildingData={selectedBuilding}
                facility={facility}
                fetchBuildings={fetchBuildings}
              />
            </div>
          {/* </div> */}
        </div>
      )}
    </div>
  );
};

export default Building;
