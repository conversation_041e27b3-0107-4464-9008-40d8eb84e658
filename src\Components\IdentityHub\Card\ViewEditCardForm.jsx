import React, { useState, useEffect } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { editCard } from "../../../api/identity";
import { toast } from "react-toastify";
import { useCardData } from "../../../hooks/useCardData";

const ViewEditCardForm = ({ cardData, onUpdate, onClose }) => {
  const {
    statusOptions,
    formatOptions,
    templateOptions,
    statusMap,
    formatMap,
    templateMap,
  } = useCardData();
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: cardData.card_id || "",
    card_number: cardData.card_number || "",
    card_format: typeof cardData.card_format === 'number' ? cardData.card_format : parseInt(cardData.card_format, 10) || 0,
    facility_code: cardData.facility_code || "",
    pin: cardData.pin || "",
    template: typeof cardData.template === 'number' ? cardData.template : parseInt(cardData.template, 10) || 0,
    active_date: cardData.active_date || "",
    deactive_date: cardData.deactive_date || "",
    status: typeof cardData.status === 'number' ? cardData.status : parseInt(cardData.status, 10) || 0,
    reason: cardData.reason || "",
  });
  const [show, setShow] = useState(false);

  useEffect(() => {
    setFormData({
      id: cardData.card_id || "",
      card_number: cardData.card_number || "",
      card_format: typeof cardData.card_format === 'number' ? cardData.card_format : parseInt(cardData.card_format, 10) || 0,
      facility_code: cardData.facility_code || "",
      pin: cardData.pin || "",
      template: typeof cardData.template === 'number' ? cardData.template : parseInt(cardData.template, 10) || 0,
      active_date: cardData.active_date || "",
      deactive_date: cardData.deactive_date || "",
      status: typeof cardData.status === 'number' ? cardData.status : parseInt(cardData.status, 10) || 0,
      reason: cardData.reason || "",
    });
  }, [cardData]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleDateChange = (name, date) => {
    const dateValue = date ? date.toISOString().split("T")[0] : "";
    setFormData((prev) => ({ ...prev, [name]: dateValue }));
  };

  const handleSave = async (e) => {
    e.preventDefault();
    const { id, ...payload } = formData;
    try {
      const updatedCard = await editCard(formData.id, payload);
      toast.success("Card updated successfully!");
      onUpdate(updatedCard);
      setIsEditMode(false);
    } catch (error) {
      toast.error("Failed to update card. Please try again.");
      console.error("Error updating card:", error);
    }
  };

  const inputClassName = `w-full bg-transparent rounded ${isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
    }`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >

        <div className="w-full">
          <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
            <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
              Card Details
            </h2>
            <button
              className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
              type="button"
              onClick={() => {
                setShow(false);
                setTimeout(onClose, 700);
              }}
            >
              &times;
            </button>
          </div>


          <div className="p-6">
            <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
              <form onSubmit={handleSave} className="p-6">

                {/* Card Number */}
                <div className="flex items-center mb-4">
                  <label
                    htmlFor="card_number"
                    className="w-1/4 text-[16px] font-normal"
                  >
                    Card Number
                  </label>
                  <div className="w-3/4">
                    <Input
                      type="text"
                      name="card_number"
                      id="card_number"
                      value={formData.card_number}
                      onChange={handleChange}
                      disabled={!isEditMode}
                      className={inputClassName}
                    />
                  </div>
                </div>

                {/* Card Format */}
                <div className="flex items-center mb-4">
                  <label
                    htmlFor="card_format"
                    className="w-1/4 text-[16px] font-normal"
                  >
                    Card Format
                  </label>
                  <div className="w-3/4">
                    {isEditMode ? (
                      <CustomDropdown
                        options={formatOptions}
                        placeholder="Select Card Format"
                        selectedOption={formData.card_format}
                        onSelect={(val) =>
                          setFormData({ ...formData, card_format: val })
                        }
                        className="h-11 rounded border-gray-300"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="card_format"
                        id="card_format"
                        value={formatMap[formData.card_format] ?? "Unknown Format"}

                        disabled
                        className={inputClassName}
                      />
                    )}
                  </div>
                </div>

                {/* Facility Code */}
                <div className="flex items-center mb-4">
                  <label
                    htmlFor="facility_code"
                    className="w-1/4 text-[16px] font-normal"
                  >
                    Facility Code
                  </label>
                  <div className="w-3/4">
                    <Input
                      type="text"
                      name="facility_code"
                      id="facility_code"
                      value={formData.facility_code}
                      onChange={handleChange}
                      disabled={!isEditMode}
                      className={inputClassName}
                    />
                  </div>
                </div>

                {/* Pin */}
                <div className="flex items-center mb-4">
                  <label htmlFor="pin" className="w-1/4 text-[16px] font-normal">
                    Pin
                  </label>
                  <div className="w-3/4">
                    <Input
                      type="text"
                      name="pin"
                      id="pin"
                      value={formData.pin}
                      onChange={handleChange}
                      disabled={!isEditMode}
                      className={inputClassName}
                    />
                  </div>
                </div>

                {/* Template */}
                <div className="flex items-center mb-4">
                  <label htmlFor="template" className="w-1/4 text-[16px] font-normal">
                    Template
                  </label>
                  <div className="w-3/4">
                    {isEditMode ? (
                      <CustomDropdown
                        options={templateOptions}
                        placeholder="Select Template"
                        selectedOption={formData.template}
                        onSelect={(val) => setFormData({ ...formData, template: val })}
                        className="h-11 rounded border-gray-300"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="template"
                        id="template"
                        value={templateMap[formData.template] || "Unknown Template"}
                        disabled
                        className={inputClassName}
                      />
                    )}
                  </div>
                </div>

                {/* Activation Date */}
                <div className="flex items-center mb-4">
                  <label htmlFor="activation" className="w-1/4 text-[16px] font-normal">
                    Activation Date
                  </label>
                  <div className="w-3/4">
                    {isEditMode ? (
                      <DateInput
                        value={formData.active_date}
                        onChange={(selectedDate) =>
                          handleDateChange("active_date", selectedDate)
                        }
                        placeholder="MM-DD-YYYY"
                        className={inputClassName}
                      />
                    ) : (
                      <Input
                        type="text"
                        name="activation"
                        id="activation"
                        value={formData.active_date}
                        disabled
                        className={inputClassName}
                      />
                    )}
                  </div>
                </div>

                {/* Deactivation Date */}
                <div className="flex items-center mb-4">
                  <label
                    htmlFor="deactivation"
                    className="w-1/4 text-[16px] font-normal"
                  >
                    Deactivation Date
                  </label>
                  <div className="w-3/4">
                    {isEditMode ? (
                      <DateInput
                        value={formData.deactive_date}
                        onChange={(selectedDate) =>
                          handleDateChange("deactive_date", selectedDate)
                        }
                        placeholder="MM-DD-YYYY"
                        className={inputClassName}
                      />
                    ) : (
                      <Input
                        type="text"
                        name="deactivation"
                        id="deactivation"
                        value={formData.deactive_date}
                        disabled
                        className={inputClassName}
                      />
                    )}
                  </div>
                </div>

                {/* Status */}
                <div className="flex items-center mb-4">
                  <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
                    Status
                  </label>
                  <div className="w-3/4">
                    {isEditMode ? (
                      <CustomDropdown
                        options={statusOptions}
                        placeholder="Select Status"
                        selectedOption={formData.status}
                        onSelect={(val) => setFormData({ ...formData, status: val })}
                        className="h-11 rounded border-gray-300"
                        hoverBgColor="hover:bg-[#4F2683]"
                      />
                    ) : (
                      <Input
                        type="text"
                        name="status"
                        id="status"
                        value={statusMap[formData.status] || "Unknown Status"}
                        disabled
                        className={inputClassName}
                      />
                    )}
                  </div>
                </div>

                {/* Reason */}
                <div className="flex items-center mb-4">
                  <label htmlFor="reason" className="w-1/4 text-[16px] font-normal">
                    Reason
                  </label>
                  <div className="w-3/4">
                    <Input
                      type="text"
                      name="reason"
                      id="reason"
                      value={formData.reason}
                      onChange={handleChange}
                      disabled={!isEditMode}
                      className={inputClassName}
                    />
                  </div>
                </div>

                <div className="flex gap-4 justify-end">
                  {!isEditMode ? (
                    <button
                      type="button"
                      onClick={(e) => {
                        e.preventDefault();
                        setIsEditMode(true);
                      }}
                      className="px-4 py-2 bg-[#4F2683] text-white rounded"
                    >
                      Edit
                    </button>
                  ) : (
                    <>
                      <button
                        type="button"
                        onClick={() => {
                          setIsEditMode(false);
                          setFormData({
                            id: cardData.card_id || "",
                            card_number: cardData.card_number || "",
                            card_format: cardData.card_format || "",
                            facility_code: cardData.facility_code || "",
                            pin: cardData.pin || "",
                            template: cardData.template || "",
                            active_date: cardData.active_date || "",
                            deactive_date: cardData.deactive_date || "",
                            status: cardData.status || "",
                            reason: cardData.reason || "",
                          });
                        }}
                        className="px-4 py-2 bg-gray-400 text-white rounded"
                      >
                        Cancel
                      </button>
                      <button
                        type="submit"
                        className="px-4 py-2 bg-[#4F2683] text-white rounded"
                      >
                        Save
                      </button>
                    </>
                  )}
                </div>

              </form>
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default ViewEditCardForm;
