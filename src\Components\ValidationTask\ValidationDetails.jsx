import React, { useState } from 'react';
import EditableSection from '../Global/EditabelForIdentity';

const ValidationDetails = (name) => {
    const [validationData, setValidatioonData] = useState({
        TaskType: { label: "Task Type", value: "Area Owner Validation " },
        TaskId: { label: "Task ID", value: "1590" },
        TaskOwner: { label: "Task Owner", value: "Randall Moran" },
        Reassign: { label: "Reassign", value: "-" },
        Description: { label: "Description", value: "Area Owner Validation " },
        Status: { label: "Status", value: "Pending" },
        DueIn: { label: "Due In", value: "04 Days" },
        Duration: { label: "Duration", value: "360d" },
        EndDate: { label: "End Date", value: "10/5/2025" },
    });
    
    const [progressData, setProgressData] = useState({
        Total: { label: "Total Validation Task", value: "3" },
        Completed: { label: "Completed", value: "0" },
        Pending: { label: "Pending", value: "3" },
        Percentage: { label: "% Completed", value: "0%" },
    });
    
 
    const handleInputChange = (section, key, value) => {
        if (section === 'Validation') {
            setValidatioonData((prev) => ({ ...prev, [key]: value }));
        } else if (section === 'Progress') {
            setProgressData((prev) => ({ ...prev, [key]: value }));
        }
    };

    return (
        <div className=" ml-4 bg-gray-100 min-h-screen">
            <EditableSection
                title="Validation Details"
                data={validationData}
                onChange={(key, value) => handleInputChange('Validation', key, value)}
                editingButton={false}
            />
           
            <EditableSection
                title="Progress"
                data={progressData}
                onChange={(key, value) => handleInputChange('Progress', key, value)}
                editingButton={false}
            />
        </div>
    );
};

export default ValidationDetails;
