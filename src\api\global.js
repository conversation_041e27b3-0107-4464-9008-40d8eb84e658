import api from "./";

/**
 * Retrieves master data for the specified groups.
 *
 * @param {Object} params - Query parameters.
 * @param {string|string[]} params.groups - One or more master data groups to retrieve.
 *   For example: 'facility_status' or ['facility_status', 'facility_type'].
 * @returns {Promise<Object>} A promise that resolves to an object containing master data.
 */
export const getMasterData = async ({ groups }) => {
  if (!groups) {
    throw new Error("groups parameter is required for getMasterData");
  }
  const response = await api.get("/master-data", { params: { groups } });
  return response.data;
};

/**
 * Retrieves all facilities with specific attributes or filters.
 *
 * @param {Object} params - Query parameters to filter facilities.
 *   For example: { attributes: ['id', 'name'], page: 1, limit: 20 }.
 * @returns {Promise<any>} A promise that resolves to the response data containing facility details.
 */
export const getFacilities = async (params) => {
  const response = await api.get("/master-data/facilities", { params });
  return response.data;
};

/**
 * Retrieves a list of buildings for a specific facility.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated building details.
 */
export const getBuildingsByFacility = async (facilityId) => {
  const response = await api.get(`master-data/buildings/${facilityId}`);
  return response.data;
};

/**
 * Retrieves a list of floors for a specific building.
 *
 * @param {string} buildingId - The unique identifier of the building.
 * @returns {Promise<any>} A promise that resolves to the response data containing floor details.
 */
export const getFloorsByBuilding = async (buildingId) => {
  const response = await api.get(`master-data/floors/${buildingId}`);
  return response.data;
};

/**
 * Retrieves a list of rooms for a specific floor.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @returns {Promise<any>} A promise that resolves to the response data containing room details.
 */
export const getRoomsByFloor = async (floorId) => {
  const response = await api.get(`master-data/rooms/${floorId}`);
  return response.data;
};

/**
 * Retrieves the list of all countries.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing country details.
 */
export const getCountries = async () => {
  const response = await api.get("/master-data/countries");
  return response.data;
};
/**
 * Retrieves the list of states for a specific country.
 *
 * @param {string} countryId - The unique identifier of the country.
 * @returns {Promise<any>} A promise that resolves to the response data containing state details.
 */
export const getStates = async (countryId) => {
  const response = await api.get(`/master-data/states/${countryId}`);
  return response.data;
};

/**
 * Retrieves the list of all timezones.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing timezone details.
 */
export const getTimezones = async () => {
  const response = await api.get("/master-data/timezones");
  return response.data;
};

/**
 * Retrieves the list of patient guest types.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing patient guest type details.
 */
export const getPatientGuestTypes = async () => {
  // 'patient_guest_relation_type' group को request करें
  const response = await api.get("/master-data", { 
    params: { 
      groups: "patient_guest_relation_type" 
    }
  });
  return response.data;
};
/**
 * Retrieves media for a specific model and media reference UUID.
 *
 * @param {string} model - The model name from which to fetch the media (e.g., "Facility").
 * @param {Object} params - Query parameters.
 * @param {string} [params.key="image"] - The media key (defaults to "image" if not provided).
 * @param {string} params.value - The media reference UUID.
 * @returns {Promise<any>} A promise that resolves to the response data containing media details.
 */
export const getMediaByModel = async (model, { key = "documents", value }) => {
  const response = await api.get(`/master-data/media/${model}`, {
    params: { key, value },
  });
  return response.data.data;
};



/**
 * Searches identities by name, eid, or search string.
 *
 * @param {string} search - The search string for name or eid.
 * @returns {Promise<any>} A promise that resolves to the response data containing matching identities.
 */
export const searchIdentities = async (search) => {
  const response = await api.get("/master-data/identity-hub", { params: { search } });
  return response.data;
};

/**
 * Retrieves all identities from the identity hub.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing all identities.
 */
export const getIdentities = async () => {
  const response = await api.get("/master-data/identity-hub");
  return response.data;
};

/**
 * Retrieves the list of all access levels.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing access level details.
 */
export const getAccessLevels = async (params = {}) => {
  const response = await api.get("/master-data/access-levels", { params });
  return response.data;
};

/**
 * Retrieves the list of all kiosk groups.
 *
 * @returns {Promise<any>} A promise that resolves to the response data containing kiosk group details.
 */
export const getKioskGroups = async () => {
  const response = await api.get("/master-data/kiosk-groups");
  return response.data;
};