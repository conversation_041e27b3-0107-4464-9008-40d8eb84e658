// import React, { useRef } from "react";
// import { Formik, Form } from "formik";
// import * as Yup from "yup";
// import Button from "../Button";
// import ImageCapture from "../ImageAndCamera/ImageCaptureForForm";
// import Input from "../Input/Input";
// import DateInput from "../Input/DateInput";
// import CustomDropdown from "../CustomDropdown";
// import demo from "../../../Images/fromimg.svg";

// const HostForm = ({ fieldsToRender, onAddGuest, onClose }) => {
//   // Default fields agar fieldsToRender pass na ho to sabhi fields show honge
//   const defaultFields = [
//     "patientName",
//     "facility",
//     "escortName",
//     "startDate",
//     "startTime",
//     "endTime",
//     "guestName",
//     "dob",
//     "guestMail",
//     "phoneNumber",
//     "relationship"
//   ];
//   fieldsToRender = fieldsToRender || defaultFields;

//   const submitActionRef = useRef("");
//   const facilityOptions = ["xray-room", "operation-theater"];
//   const durationOptions = ["2:00 Hour", "4:00 Hour", "8:00 Hour", "12:00 Hour"];
//   const relationshipOption = ["Select Relationship", "Son", "Daughter", "Spouse", "Friend", "Other"];

//   // Set startTime in 24-hour format
//   const currentTime = new Date().toLocaleTimeString([], { hour12: false, hour: "2-digit", minute: "2-digit" });

//   const initialValues = {
//     // Patient Information
//     patientName: "",
//     facility: "",
//     escortName: "",
//     startDate: new Date(),
//     startTime: currentTime,
//     endTime: "",
//     // Guest Information
//     guestName: "",
//     dob: "",
//     guestMail: "",
//     phoneNumber: "",
//     relationship: "",
//     image: "",
//   };

//   const validationSchema = Yup.object({
//     guestName: Yup.string().required("Guest Name is required"),
//   });

//   const handleSubmit = (values, { resetForm }) => {
//     // Create new guest object
//     const newGuest = {
//       guestName: values.guestName,
//       dob: values.dob,
//       guestMail: values.guestMail,
//       phoneNumber: values.phoneNumber,
//       relationship: values.relationship,
//       screening: true,
//       arrivalTime: `${values.startDate.toLocaleDateString()}, ${values.startTime}`,
//       image: values.image || demo,
//     };
//     onAddGuest(newGuest);

//     if (submitActionRef.current === "addMore") {
//       resetForm({
//         values: {
//           ...values,
//           guestName: "",
//           dob: new Date(),
//           guestMail: "",
//           phoneNumber: "",
//           relationship: "",
//           image: "",
//         },
//         errors: {},
//         touched: {},
//       });
//     } else if (submitActionRef.current === "save") {
//       resetForm();
//       onClose();
//     }
//   };

//   // Patient Information fields list
//   const patientFields = ["patientName", "facility", "escortName", "startDate", "startTime", "endTime"];
//   const hasPatientFields = fieldsToRender.some((field) => patientFields.includes(field));

//   return (
//     <div className="px-12">
//       <Formik
//         initialValues={initialValues}
//         validationSchema={validationSchema}
//         onSubmit={handleSubmit}
//         validateOnMount={false}
//       >
//         {({ setFieldValue, values, submitForm, touched, errors, handleBlur }) => (
//           <Form className="space-y-4">
//             {/* Patient Information */}
//             {hasPatientFields && (
//               <>
//                 <div className="flex justify-between">
//                   <h1 className="text-xl md:text-lg font-bold text-[#4F2683]">Create Walk In Visit</h1>
//                   <button
//                     type="button"
//                     className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
//                     onClick={onClose}
//                   >
//                     &times;
//                   </button>
//                 </div>
//                 <div className="bg-gray-100 p-4 rounded-lg border rounded-tr-3xl border-[#4F2683]">
//                   <h2 className="font-semibold text-base md:text-lg mb-4 text-[#4F2683]">Patient Information</h2>
//                   <div className="flex gap-4 w-full">
//                     {fieldsToRender.includes("facility") && (
//                       <div className="w-1/4">
//                         <h2>Facility</h2>
//                         <CustomDropdown
//                           options={facilityOptions}
//                           bgColor="bg-[white] text-black"
//                           textColor="text-black"
//                           hoverBgColor="hover:bg-[#4F2683]"
//                           borderColor="border-gray-300"
//                           rounded="rounded-[6px]"
//                           className="border h-11 p-2 focus:outline-none focus:ring-1"
//                         />
//                       </div>
//                     )}
//                     {fieldsToRender.includes("escortName") && (
//                       <div className="w-1/4">
//                         <Input
//                           type="text"
//                           label="Escort Name"
//                           name="escortName"
//                           value={values.escortName}
//                           className="border h-11 p-2 focus:outline-none rounded-[6px] focus:ring-1"
//                           placeholder="Enter Escort Name"
//                           onChange={(e) => setFieldValue("escortName", e.target.value)}
//                           onBlur={handleBlur}
//                         />
//                       </div>
//                     )}
//                     {fieldsToRender.includes("startDate") && (
//                       <div className="w-1/4">
//                         <DateInput
//                           label="Start Date"
//                           name="startDate"
//                           value={values.startDate}
//                           onChange={(date) => setFieldValue("startDate", date)}
//                           placeholder="MM-DD-YYYY"
//                           onBlur={handleBlur}
//                         />
//                       </div>
//                     )}
//                     {fieldsToRender.includes("startTime") && (
//                       <div className="w-1/4">
//                         <Input
//                           type="time"
//                           label="Start Time"
//                           name="startTime"
//                           value={values.startTime}
//                           onChange={(e) => setFieldValue("startTime", e.target.value)}
//                           onBlur={handleBlur}
//                         />
//                       </div>
//                     )}
//                     {fieldsToRender.includes("endTime") && (
//                       <div className="w-1/4">
//                         <h2>Duration</h2>
//                         <CustomDropdown
//                           options={durationOptions}
//                           defaultValue="4:00 Hour"
//                           bgColor="bg-[white] text-black"
//                           textColor="text-black"
//                           hoverBgColor="hover:bg-[#4F2683]"
//                           borderColor="border-gray-300"
//                           rounded="rounded-[6px]"
//                           className="border h-11 p-2 focus:outline-none focus:ring-1"
//                         />
//                       </div>
//                     )}
//                     {fieldsToRender.includes("patientName") && (
//                       <div className="w-1/4">
//                         <Input
//                           type="text"
//                           label="Patient Name"
//                           name="patientName"
//                           value={values.patientName}
//                           placeholder="Enter Patient Name"
//                           onChange={(e) => setFieldValue("patientName", e.target.value)}
//                           onBlur={handleBlur}
//                         />
//                       </div>
//                     )}
//                   </div>
//                 </div>
//               </>
//             )}

//             {/* Guest Information */}
//             <div className="rounded-lg border p-4">
//               <div className="flex justify-between mb-4">
//                 <h2 className="font-normal text-base md:text-lg text-[#4F2683]">Guest Information</h2>
//                 {!hasPatientFields && (
//                   <button
//                     type="button"
//                     className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
//                     onClick={onClose}
//                   >
//                     &times;
//                   </button>
//                 )}
//               </div>
//               <div>
//                 <ImageCapture
//                   onImageCaptured={(img) => setFieldValue("image", img)}
//                   onImageUploaded={(img) => setFieldValue("image", img)}
//                 />
//               </div>
//               <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-16">
//                 {fieldsToRender.includes("guestName") && (
//                   <div className="w-full">
//                     <Input
//                       type="text"
//                       label="Guest Name*"
//                       name="guestName"
//                       value={values.guestName}
//                       placeholder="Enter Guest Name"
//                       onChange={(e) => setFieldValue("guestName", e.target.value)}
//                       onBlur={handleBlur}
//                     />
//                     {touched.guestName && errors.guestName && (
//                       <div className="text-red-500 text-sm">{errors.guestName}</div>
//                     )}
//                   </div>
//                 )}
//                 {fieldsToRender.includes("dob") && (
//                   <div className="w-full">
//                     <DateInput
//                       label="Date of Birth"
//                       name="dob"
//                       value={values.dob}
//                       className="h-11"
//                       onChange={(date) => setFieldValue("dob", date)}
//                       placeholder="Select a date"
//                       onBlur={handleBlur}
//                     />
//                   </div>
//                 )}
//                 {fieldsToRender.includes("guestMail") && (
//                   <div className="w-full">
//                     <Input
//                       type="email"
//                       label="Guest Mail"
//                       name="guestMail"
//                       value={values.guestMail}
//                       placeholder="Enter Guest Email"
//                       onChange={(e) => setFieldValue("guestMail", e.target.value)}
//                       onBlur={handleBlur}
//                     />
//                   </div>
//                 )}
//               </div>
//               <div className="flex justify-center gap-5 px-32 pt-4">
//                 {fieldsToRender.includes("phoneNumber") && (
//                   <div className="w-full">
//                     <Input
//                       type="tel"
//                       label="Phone Number"
//                       name="phoneNumber"
//                       value={values.phoneNumber}
//                       placeholder="Enter Phone Number"
//                       onChange={(e) => setFieldValue("phoneNumber", e.target.value)}
//                       onBlur={handleBlur}
//                     />
//                   </div>
//                 )}
//                 {fieldsToRender.includes("relationship") && (
//                   <div className="w-full">
//                     <h2>Relationship</h2>
//                     <CustomDropdown
//                       options={relationshipOption}
//                       bgColor="bg-[white] text-black"
//                       textColor="text-black"
//                       hoverBgColor="hover:bg-[#4F2683]"
//                       borderColor="border-gray-300"
//                       rounded="rounded-[6px]"
//                       className="p-2 border h-11 rounded-[10px] focus:outline-none focus:ring-1"
//                     />
//                   </div>
//                 )}
//               </div>
//             </div>

//             {/* Submission Buttons */}
//             <div className="flex gap-4 pl-4 justify-center pb-4">
//               <Button
//                 buttonType="button"
//                 type="primary"
//                 onClick={() => {
//                   submitActionRef.current = "addMore";
//                   submitForm();
//                   console.log("Add More");
//                 }}
//                 label="Add More"
//               />
//               <Button
//                 type="primary"
//                 onClick={() => {
//                   submitActionRef.current = "save";
//                   submitForm();
//                 }}
//                 label="Save"
//               />
//             </div>
//           </Form>
//         )}
//       </Formik>
//     </div>
//   );
// };

// export default HostForm;
