import React from "react";
import GenericTable from "../../../Components/GenericTable";
import DetailsCard from "../../../Components/Global/DetailsCard";
import userImg from "../../../Images/guest-image2.png"; 

const MyHistory = ({ guest, onClose }) => {
  const historyData = [
    {
      guestId: "352672",
      category: "Scheduled",
      host: "<PERSON>, 234...",
      checkInTime: "19-Mar-2025 | 11:46 PM",
      checkOutTime: "19-Mar-2025 | 11:46 PM",
      status: "Check-in",
    },
    {
      guestId: "752671",
      category: "Walk In",
      host: "<PERSON>, 324...",
      checkInTime: "19-Mar-2025 | 11:46 PM",
      checkOutTime: "19-Mar-2025 | 11:46 PM",
      status: "Check-out",
    },
    // ...other history records...
  ];

  const columns = [
    { name: "Guest ID", selector: (row) => row.guestId },
    { name: "Category", selector: (row) => row.category },
    { name: "Host", selector: (row) => row.host },
    { name: "Check In Time", selector: (row) => row.checkInTime },
    { name: "Check Out Time", selector: (row) => row.checkOutTime },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center  rounded-full ${
            row.status.toLowerCase() === "check-in"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-5xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            View Request List
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <div className="p-6">
          <DetailsCard
            OpenPhotoModal={() => { }}
            profileImage={guest.profileImage || userImg}
            defaultImage={userImg}
            name={guest.name}
            additionalFields={[
              { label: "Company", value: guest.eid || "N/A" },
              { label: "Gmail", value: guest.company || "N/A" },
              { label: "Is Private?", value: guest.email || "N/A" },
            ]}
          />

          

          <GenericTable
            title="Guest History"
            columns={columns}
            data={historyData}
            showAddButton={false} // No add button needed
          />
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 bg-gray-400 text-white rounded"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MyHistory;
