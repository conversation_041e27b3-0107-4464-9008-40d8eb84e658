import React, { useState } from 'react';
import EditableSection from '../../Components/Global/EditableSection';

const GroupDetails = () => {
  const [details, setDetails] = useState({
    Name: 'Group Name',
    GroupId: '123NR',
    AreaId: '123', 
    RiskLevel: 'High',
    Status: 'Active',
  });

  const [configuration, setConfiguration] = useState({
    RequestableByAdmin: 'Yes', 
    RequestableInSelfService: 'Yes',
    AreaSpecialInstruction: 'Access group for GPS Executives only',
  });

  const handleInputChange = (section, key, value) => {
    if (section === 'details') {
      setDetails((prev) => ({ ...prev, [key]: value }));
    } else if (section === 'configuration') {
      setConfiguration((prev) => ({ ...prev, [key]: value }));
    }
  };

  return (
    <div className="bg-gray-100 min-h-screen">
     
      <EditableSection
        title="Group Info"
        data={details}
        onChange={(key, value) => handleInputChange('details', key, value)}
        editableKeys={['Name', 'RiskLevel']}
      />

      <EditableSection
        title="Configuration"
        data={configuration}
        onChange={(key, value) => handleInputChange('configuration', key, value)}
        dropdownKeys={['RequestableByAdmin', 'RequestableInSelfService']}
        dropdownOptions={{
          RequestableByAdmin: ['Yes', 'No'],
          RequestableInSelfService: ['Yes', 'No'],
        }}
      />
    </div>
  );
};

export default GroupDetails;
