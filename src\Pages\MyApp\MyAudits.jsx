import React, { useState } from 'react';
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { HiOutlineHandThumbUp, HiOutlineHandThumbDown } from "react-icons/hi2";
import { IoFilter } from "react-icons/io5";
import { validationTask } from '../../api/static';
import TruncatedCell from '../../Components/Tooltip/TruncatedCell';
import TruncatedRow from '../../Components/Tooltip/TrucantedRow';
import FilterPanel from '../../Components/Observation/FilterPanel';
import newWindow from "../../Images/new-window.svg";
import { useTranslation } from 'react-i18next';

const MyAudits = () => {
  const { t } = useTranslation();
  // State to manage filtered data
  const [filteredData, setFilteredData] = useState(validationTask);
  const [activeFilter, setActiveFilter] = useState('All');
  const [searchQuery, setSearchQuery] = useState('');
   // Filter panel toggle
    const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);
      const [hoveredRow, setHoveredRow] = useState(null);

      const updateRecStatus = (taskId, newStatus) => {
        setFilteredData(prevData =>
          prevData.map(task =>
            task.taskId === taskId ? { ...task, rec: newStatus } : task
          )
        );
      };
  // Columns for the table
  const columns = [
    {
      name: <TruncatedCell text={t('my_audits.task_id')} />,
      selector: row => row.taskId,
      sortable: true,
      cell: (row, index) => (
        <div
          className="relative flex items-center space-x-3 cursor-pointer group w-full"
          onMouseEnter={() => setHoveredRow(index)}
          onMouseLeave={() => setHoveredRow(null)}
          onClick={() =>
            window.open(
              `/validation-details?name=${encodeURIComponent(row.taskOwner)}`,
              "_blank"
            )
          }
        >
          <span>{row.taskId}</span>
          {hoveredRow === index && (
            <img className="w-4 h-4" src={newWindow} alt={t('my_audits.open')} />
          )}
        </div>
      ),
    },
    {
      name: <TruncatedCell text={t('my_audits.description')} />,
      selector: row => row.description,
      cell: row => <TruncatedRow text={row.description} />,
    },
    {
      name: <TruncatedCell text={t('my_audits.run_id')} />,
      selector: row => row.runId,
      cell: row => <TruncatedRow text={row.runId} />,
    },
    {
      name: t('my_audits.task_owner'),
      selector: row => row.taskOwner,
      cell: row => <TruncatedRow text={row.taskOwner} />,
    },
    {
      name: t('my_audits.start_date'),
      selector: row => row.startDate,
    },
    {
      name: t('my_audits.end_date'),
      selector: row => row.endDate,
      center: true,
    },
    {
      name: <TruncatedCell text={t('my_audits.recommend')}/>,
      selector: row =>
        row.rec === "ok" ? (
          <HiOutlineHandThumbUp className="text-[#4F2683] text-xl" />
        ) : (
          <HiOutlineHandThumbDown className="text-[#8F8F8F] text-xl" />
        ),
      center: true,
    },
    {
      name: t('my_audits.action'),
      cell: (row) => (
        <div className="flex gap-2 ">
          <button className='flex gap-1 p-1 whitespace-nowrap w-full border text-[#4F2683] rounded-full'
          onClick={() => updateRecStatus(row.taskId, "ok")}> 
            <HiOutlineHandThumbUp className=" text-xl" />
            <p>{t('my_audits.retain')}</p>
          </button>
          <button className='flex gap-1 p-1 whitespace-nowrap w-full border text-[#8F8F8F] rounded-full'
          onClick={() => updateRecStatus(row.taskId, "notOk")}> 
            <HiOutlineHandThumbDown className=" text-xl" />
            <p>{t('my_audits.remove')}</p>
          </button>
        </div>
      ),
      width: "130px",
      center: true,
    },
    {
      name: t('my_audits.status'),
      selector: row => row.status,
      cell: row => (
        <span
          className={`w-24 p-1 flex justify-center items-center rounded-full ${row.status.toLowerCase() === "complete"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
        >
          {row.status}
        </span>
      ),
      center: true,
    },
  ];

  // Search handler
  const handleSearch = e => {
    const query = e.target.value.toLowerCase();
    setSearchQuery(query);
    setFilteredData(
      validationTask.filter(item =>
        item.status.toLowerCase().includes(query)
      )
    );
  };

  const handleFilterChange = filter => {
    setActiveFilter(filter);
    if (filter === "All") {
      setFilteredData(validationTask);
    } else if (filter === "Active") {
      setFilteredData(validationTask.filter(item => item.status === "complete"));
    }
  };

  return (
    <div className="pt-20 pl-24">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">
          {t('my_audits.validation_task')}
        </h2>
      </div>

      {/* Filter Buttons */}
      <div className="flex justify-start mb-4">
        <FilterButtons
          filter={activeFilter}
          onFilterChange={handleFilterChange}
          filterOptions={[
            { label: t('my_audits.all'), value: "All" },
            { label: t('my_audits.active'), value: "Active" },
          ]}
        />
      </div>

      {/* Table */}
      <div className="bg-white rounded-[10px]">
        <GenericTable
          fixedHeaderScrollHeight="400"
          title={t('my_audits.validation_tasks')}
          searchTerm={searchQuery}
          onSearchChange={handleSearch}
          columns={columns}
          data={filteredData}
          showSearch={true}
          showAddButton={false}
          extraControls={
            <IoFilter
              className="bg-white shadow-sm border p-1 text-[#4F2683] h-8 w-8 rounded cursor-pointer"
              onClick={() => setIsFilterPanelOpen(true)}
            />
          }
        />
        <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
      </div>
    </div>
  );
};

export default MyAudits;
