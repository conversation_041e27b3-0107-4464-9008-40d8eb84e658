import React from 'react';
import { useDispatch } from 'react-redux';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
} from '@mui/material';

import { toggleTemplateManager } from '../../redux/idDesignerSlice';

const TemplateManager = () => {
  const dispatch = useDispatch();

  const handleClose = () => {
    dispatch(toggleTemplateManager());
  };

  return (
    <Dialog
      open={true}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
    >
      <DialogTitle>
        Template Manager
      </DialogTitle>
      
      <DialogContent>
        <Box sx={{ minHeight: 400, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            Template management functionality will be implemented here.
            <br />
            Features will include:
            <br />
            • Save current template
            <br />
            • Load existing templates
            <br />
            • Template versioning
            <br />
            • Import/Export templates
          </Typography>
        </Box>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={handleClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default TemplateManager;
