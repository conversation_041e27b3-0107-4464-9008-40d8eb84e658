import React from 'react';
import { Box } from '@mui/material';
import { fromPixels, UNITS } from '../../utils/unitConversion';

const Ruler = ({
  orientation = 'horizontal',
  length,
  scale = 1,
  offset = 0,
  unit = UNITS.INCHES,
  dpi = 300,
  thickness = 30,
  canvasWidth = 800,
  canvasHeight = 500,
  stageWidth = 800,
  stageHeight = 500
}) => {
  const isHorizontal = orientation === 'horizontal';
  
  // Calculate the number of major and minor divisions
  const getMajorDivisionSize = () => {
    switch (unit) {
      case UNITS.INCHES:
        return 1; // 1 inch major divisions
      case UNITS.MILLIMETERS:
        return 10; // 10mm major divisions
      default:
        return 100; // 100px major divisions
    }
  };
  
  const getMinorDivisionSize = () => {
    switch (unit) {
      case UNITS.INCHES:
        return 0.125; // 1/8 inch minor divisions
      case UNITS.MILLIMETERS:
        return 1; // 1mm minor divisions
      default:
        return 10; // 10px minor divisions
    }
  };
  
  const majorDivisionSize = getMajorDivisionSize();
  const minorDivisionSize = getMinorDivisionSize();
  
  // Convert length from pixels to current unit
  const lengthInUnit = fromPixels(length, unit, dpi);
  
  // Generate tick marks and labels
  const generateTicks = () => {
    const ticks = [];
    const labels = [];

    // Canvas starts at the offset position (which is the fixed padding)
    const canvasStartPos = offset;

    // Calculate the range of units to display
    const canvasLengthInUnits = isHorizontal
      ? fromPixels(canvasWidth, unit, dpi)
      : fromPixels(canvasHeight, unit, dpi);

    // Generate major ticks
    for (let i = 0; i <= Math.ceil(canvasLengthInUnits / majorDivisionSize) * majorDivisionSize; i += majorDivisionSize) {
      // Convert unit position to pixels and apply scale
      const pixelPosition = unit === UNITS.INCHES ? i * dpi : i * dpi / 25.4;
      const position = canvasStartPos + (pixelPosition * scale);

      if (position >= 0 && position <= length) {
        ticks.push({
          position,
          height: thickness * 0.8,
          isMajor: true,
          value: i
        });

        // Add label for major ticks
        labels.push({
          position,
          value: i,
          label: unit === UNITS.INCHES ? i.toString() : Math.round(i).toString()
        });
      }
    }

    // Generate minor ticks
    for (let i = 0; i <= Math.ceil(canvasLengthInUnits / minorDivisionSize) * minorDivisionSize; i += minorDivisionSize) {
      // Convert unit position to pixels and apply scale
      const pixelPosition = unit === UNITS.INCHES ? i * dpi : i * dpi / 25.4;
      const position = canvasStartPos + (pixelPosition * scale);

      if (position >= 0 && position <= length && i % majorDivisionSize !== 0) {
        ticks.push({
          position,
          height: thickness * 0.4,
          isMajor: false,
          value: i
        });
      }
    }

    return { ticks, labels };
  };
  
  const { ticks, labels } = generateTicks();
  
  const getUnitSymbol = () => {
    switch (unit) {
      case UNITS.INCHES:
        return '"';
      case UNITS.MILLIMETERS:
        return 'mm';
      default:
        return 'px';
    }
  };
  
  return (
    <Box
      sx={{
        position: 'relative',
        width: isHorizontal ? length : thickness,
        height: isHorizontal ? thickness : length,
        backgroundColor: '#f5f5f5',
        borderBottom: isHorizontal ? '1px solid #ddd' : 'none',
        borderRight: !isHorizontal ? '1px solid #ddd' : 'none',
        overflow: 'hidden',
        userSelect: 'none',
        fontSize: '10px',
        fontFamily: 'monospace',
      }}
    >
      {/* Ruler background */}
      <Box
        sx={{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          background: 'linear-gradient(to ' + (isHorizontal ? 'right' : 'bottom') + ', #f8f8f8 0%, #f0f0f0 100%)',
        }}
      />
      
      {/* Tick marks */}
      {ticks.map((tick, index) => (
        <Box
          key={index}
          sx={{
            position: 'absolute',
            backgroundColor: tick.isMajor ? '#333' : '#666',
            ...(isHorizontal ? {
              left: tick.position,
              top: thickness - tick.height,
              width: '1px',
              height: tick.height,
            } : {
              top: tick.position,
              left: thickness - tick.height,
              width: tick.height,
              height: '1px',
            })
          }}
        />
      ))}
      
      {/* Labels */}
      {labels.map((label, index) => (
        <Box
          key={index}
          sx={{
            position: 'absolute',
            color: '#333',
            fontSize: '9px',
            fontWeight: 500,
            ...(isHorizontal ? {
              left: label.position - 10,
              top: 2,
              width: 20,
              textAlign: 'center',
            } : {
              top: label.position - 6,
              left: 2,
              width: thickness - 4,
              height: 12,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transform: 'rotate(-90deg)',
              transformOrigin: 'center',
            })
          }}
        >
          {label.label}
        </Box>
      ))}
      
      {/* Unit indicator */}
      <Box
        sx={{
          position: 'absolute',
          ...(isHorizontal ? {
            right: 4,
            bottom: 2,
          } : {
            left: 2,
            bottom: 4,
          }),
          fontSize: '8px',
          color: '#666',
          fontWeight: 'bold',
        }}
      >
        {getUnitSymbol()}
      </Box>
    </Box>
  );
};

export default Ruler;
