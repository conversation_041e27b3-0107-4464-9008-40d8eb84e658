import React, { useState, useRef, useEffect } from 'react';

const CustomTimePicker = () => {
  const [time, setTime] = useState('00:00');
  const [showPicker, setShowPicker] = useState(false);
  const wrapperRef = useRef(null);

  const hours = Array.from({ length: 24 }, (_, i) => i.toString().padStart(2, '0'));
  const minutes = Array.from({ length: 60 }, (_, i) => i.toString().padStart(2, '0'));

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setShowPicker(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleTimeChange = (hour, minute) => {
    setTime(`${hour}:${minute}`);
    setShowPicker(false);
  };

  const [currentHour, currentMinute] = time.split(':');

  return (
    <div className="relative top-[-10px]" ref={wrapperRef}>
      <label htmlFor="time-picker" className="block text-sm font-medium text-gray-700 mb-1">
        Select Time
      </label>
      <div className="relative ">
        <input
          id="time-picker"
          type="text"
          className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
          value={time}
          readOnly
          placeholder="HH:MM"
          onClick={() => setShowPicker(!showPicker)}
        />
        <span
          className="absolute right-3 top-2.5 text-gray-500 cursor-pointer"
          onClick={() => setShowPicker(!showPicker)}
        >
          🕒
        </span>
        {showPicker && (
          <div className="absolute top-full left-0 w-full bg-white border border-gray-300 rounded-md shadow-lg mt-1 z-10">
            <div className="flex divide-x divide-gray-200">
              <div className="w-1/2 max-h-48 overflow-y-auto">
                <h4 className="text-sm font-medium text-gray-700 px-3 py-2">Hours</h4>
                {hours.map((hour) => (
                  <div
                    key={hour}
                    className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${
                      hour === currentHour ? 'bg-[#4F2683] text-white' : ''
                    }`}
                    onClick={() => handleTimeChange(hour, currentMinute)}
                  >
                    {hour}
                  </div>
                ))}
              </div>
              <div className="w-1/2 max-h-48 overflow-y-auto">
                <h4 className="text-sm font-medium text-gray-700 px-3 py-2">Minutes</h4>
                {minutes.map((minute) => (
                  <div
                    key={minute}
                    className={`px-3 py-2 cursor-pointer hover:bg-gray-100 ${
                      minute === currentMinute ? 'bg-[#4F2683] text-white' : ''
                    }`}
                    onClick={() => handleTimeChange(currentHour, minute)}
                  >
                    {minute}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CustomTimePicker;