import { useState, useEffect } from "react";
import { getTimezones } from "../api/global";
import { toast } from "react-toastify";

let timezoneMasterDataCache = null;

export const useTimezoneMasterData = () => {
  const [timezones, setTimezones] = useState([]);

  useEffect(() => {
    const fetchTimezones = async () => {
      if (timezoneMasterDataCache) {
        setTimezones(timezoneMasterDataCache);
        return;
      }
      try {
        const res = await getTimezones();
        if (Array.isArray(res.data)) {
          timezoneMasterDataCache = res.data;
          setTimezones(res.data);
        } else {
          throw new Error("Invalid response");
        }
      } catch (error) {
        toast.error("Error fetching timezone data");
      }
    };

    fetchTimezones();
  }, []);

  return { timezones };
};
