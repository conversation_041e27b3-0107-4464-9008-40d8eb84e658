import React, { useState } from "react";
import Swal from "sweetalert2"; // Import SweetAlert2
import GenericTable from "../../Components/GenericTable";
import { useNavigate } from "react-router-dom"; // Import useNavigate
import RemoveAccessRequest from "../../Components/MyApp/MyRequest/RemoveAccessRequest"; // Import the component
import { useTranslation } from "react-i18next"; // Import useTranslation

const Request = () => {
  const navigate = useNavigate(); // Initialize navigate function
  const { t } = useTranslation(); // Initialize translation function
  const [searchTerm, setSearchTerm] = useState("");
  const [requests, setRequests] = useState([
    {
      id: "17724260",
      type: "Request Access",
      requestedBy: "AKSHAY SARDA",
      createdOn: "04-Dec-2024",
      justification: "General Access",
      requestedFor: "AKSHAY SARDA",
      items: "NOIDA, IND",
      status: "Pending",
    },
    // ...add more rows as needed...
  ]);
  const [isRemoveModalOpen, setIsRemoveModalOpen] = useState(false); // State for modal visibility

  const handleAdd = async () => {
    const inputOptions = new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          "#ff0000": t('my_request.request_access'),
          "#00ff00": t('my_request.remove_access'),
        });
      }, 1000);
    });

    const { value: color } = await Swal.fire({
      title: t('my_request.choose_request_type'),
      input: "radio",
      inputOptions,
      inputValidator: (value) => {
        if (!value) {
          return t('my_request.choose_something');
        }
      },
    });

    if (color === "#ff0000") {
      navigate("/remove-request"); 
    } else if (color === "#00ff00") {
      setIsRemoveModalOpen(true); // Open RemoveAccessRequest modal
    } else if (color) {
      Swal.fire({ html: `${t('my_request.you_selected')}: ${color}` });
    }
  };

  const handleRemoveAccess = (data) => {
    console.log("Remove Access Data:", data);
    // Handle the remove access logic here
  };

  const columns = [
    {
      name: t('my_request.request_id'),
      selector: (row) => (
        <span
          className="text-blue-500 cursor-pointer"
          onClick={() => handleAdd()}
        >
          {row.id}
        </span>
      ),
      sortable: true,
    },
    {
      name: t('my_request.type'),
      selector: (row) => row.type,
    },
    {
      name: t('my_request.requested_by'),
      selector: (row) => row.requestedBy,
    },
    {
      name: t('my_request.created_on'),
      selector: (row) => row.createdOn,
    },
    {
      name: t('my_request.justification'),
      selector: (row) => row.justification,
    },
    {
      name: t('my_request.requested_for'),
      selector: (row) => row.requestedFor,
    },
    {
      name: t('my_request.items'),
      selector: (row) => row.items,
    },
   {
  name: t('my_request.status'),
  selector: (row) => row.status,
  cell: (row) => (
    <span
      className={`w-24 py-1 px-2 flex justify-center items-center rounded-full text-sm font-medium
        ${
          row.status.toLowerCase() === "active"
            ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
            : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
        }`}
    >
      {t(`my_request.status_${row.status.toLowerCase()}`)}
    </span>
  ),
}

  ];

  return (
    <div className="bg-[#F9F5FF] p-4 rounded-lg pl-20 pt-20">
      <GenericTable
        title={t('my_request.my_requests')}
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={requests}
        showSearch={true}
        showAddButton={true}
        onAdd={handleAdd}
      />
      {isRemoveModalOpen && (
        <RemoveAccessRequest
          onClose={() => setIsRemoveModalOpen(false)} // Close modal
          onRemoveAccess={handleRemoveAccess} // Handle remove access
        />
      )}
    </div>
  );
};

export default Request;
