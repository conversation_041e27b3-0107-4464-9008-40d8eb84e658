import React, { useState } from "react";
import Button from "../Global/Button";
import { facilityData } from "../../api/static"; // adjust path as needed

const AccessAreaAdd = ({ onSubmit, onClose }) => {
  const [facility, setFacility] = useState("");
  const [facilitySearchTerm, setFacilitySearchTerm] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [building, setBuilding] = useState("");
  const [floorNumber, setFloorNumber] = useState("");
  const [roomNo, setRoomNo] = useState("");
  const [name, setName] = useState("");
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Extract facility names from facilityData
  const facilityOptions = facilityData.map((item) => item.facility);

  // Filter facility options based on search term
  const filteredFacilities = facilityOptions.filter((option) =>
    option.toLowerCase().includes(facilitySearchTerm.toLowerCase())
  );

  const handleFacilityInputChange = (e) => {
    const value = e.target.value;
    setFacilitySearchTerm(value);
    setIsDropdownVisible(value.length > 0);
  };

  const handleFacilitySelect = (selectedOption) => {
    setFacility(selectedOption);
    setFacilitySearchTerm(selectedOption);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onClose();
    // If facility is not selected, alert and close modal.
    if (!facility) {
      alert("Please select a facility.");
      // Use setTimeout to ensure the alert is dismissed before closing the modal.
      setTimeout(() => onClose(), 0);
      return;
    }

    // Find the facility details from the API data based on the selected facility name.
    const selectedFacilityData = facilityData.find(
      (item) => item.facility === facility
    );
    if (!selectedFacilityData) {
      alert("Facility data not found.");
      setTimeout(() => onClose(), 0);
      return;
    }

    // Map API data and any additional form fields to match the table structure.
    const newAccessArea = {
      id: Date.now(), // Generate a unique id
      facility: selectedFacilityData.facility,
      code: selectedFacilityData.siteID, // Mapping siteID to code
      floor: floorNumber || "N/A",
      building: building || selectedFacilityData.address1,
      city: selectedFacilityData.city || "N/A",
      state: selectedFacilityData.state,
      country: selectedFacilityData.country,
      status: selectedFacilityData.status,
    };

    // Determine which submit button was pressed ("Add" or "Add More").
    const action = e.nativeEvent.submitter.value;
    onSubmit(newAccessArea, action);

    if (action === "add") {
      setTimeout(() => onClose(), 0);
    } else {
      // For "Add More", reset the form fields for the next entry.
      setFacility("");
      setFacilitySearchTerm("");
      setBuilding("");
      setFloorNumber("");
      setRoomNo("");
      setName("");
    }
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Document</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
          {/* Facility Search and Dropdown */}
          <div className="flex items-center mb-4">
            <label htmlFor="facility" className="text-[16px] font-normal w-1/4">
              Facility Name*
            </label>
            <div className="relative w-3/4">
              <input
                type="text"
                id="facility"
                placeholder="Search Facility"
                value={facilitySearchTerm}
                onChange={handleFacilityInputChange}
                className="w-full h-11 border border-gray-300 rounded px-3"
              />
              {isDropdownVisible && (
                <div className="absolute w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-10">
                  {filteredFacilities.length > 0 ? (
                    filteredFacilities.map((option) => (
                      <div
                        key={option}
                        className="p-2 cursor-pointer hover:bg-gray-100"
                        onClick={() => handleFacilitySelect(option)}
                      >
                        {option}
                      </div>
                    ))
                  ) : (
                    <div className="p-2 text-gray-700 text-center">
                      No Results Found.
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Additional fields (if needed) can be added below */}

          <div className="flex gap-4 justify-center">
            <Button
              type="button"
              label="Cancel"
              onClick={onClose}
              className="bg-gray-400 text-white"
            />
            <Button
              type="submit"
              label="Add"
              value="add"
              className="bg-[#4F2683] text-white"
            />
          </div>
        </form>
      </div>
      </div>
      </div>
    </div>
  );
};

export default AccessAreaAdd;
