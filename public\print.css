body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #ffffff !important; 
}

.modalPrintClass {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  border: 2px dotted #aaa;
  border-radius: 8px;
  text-align: center;
  
  background-color: #fff !important; 
  box-shadow: none; 
}

.visitor-p {
  display: inline-block;
  padding: 5px 10px;
  background-color: #5E3A8E !important; 
  color: #fff !important;
  border-radius: 5px;
  font-size: 0.9em;
  margin-bottom: 10px;
}

.guest-img {
  width: 96px;
  height: 128px;
  object-fit: cover;
  border-radius: 10px;
}

@media print {
  body {
    background-color: #ffffff !important; 
  }

  .modalPrintClass {
    background-color: #fff !important; 
  }

  .visitor-p {
    background-color: #5E3A8E !important; 
    
    color: #fff !important; 
  }

  * {
    -webkit-print-color-adjust: exact !important; 
    print-color-adjust: exact !important; 
  }
}
