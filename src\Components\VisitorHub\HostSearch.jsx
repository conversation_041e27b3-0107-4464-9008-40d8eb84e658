import React from "react";
import SearchBar from "../Global/SearchBar";

const HostSearch = ({
  placeholder,
  searchTerm,
  onInputChange,
  onSearchSubmit,
  results,
  onResultClick,
  isDropdownVisible,
  containerRef,
}) => {
  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <form onSubmit={onSearchSubmit}>
        <SearchBar
          placeholder={placeholder}
          iconSrc={""}
          onInputChange={onInputChange}
          value={searchTerm}
          borderColor="#4F2683"
        />
      </form>
      {isDropdownVisible && (
        <div
          className="w-full mt-2 border absolute p-2 bg-white z-10 rounded-md shadow-lg overflow-y-auto"
          style={{ maxHeight: "200px" }}
        >
          {results.length > 0 ? (
            results.map((host) => (
              <div
                key={host.id}
                className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
                onClick={() => onResultClick(host)}
              >
                <img
                  src={host.image}
                  alt={host.name}
                  className="w-10 h-10 rounded-lg"
                />
                <div>
                  <h2 className="font-semibold">{host.name}</h2>
                  <div className="flex flex-row gap-4">
                    <p className="text-sm text-gray-600">{host.post}</p>
                  </div>
                </div>
              </div>
            ))
          ) : (
            <p className="text-gray-500">No Records found</p>
          )}
        </div>
      )}
    </div>
  );
};

export default HostSearch;
