import React, { useState } from 'react';
import ImageCapture from './ImageCapture';
import Button from './Button'; // Importing Button component

const Modal = ({ title, onClose, onSave }) => {
    const [imageSrc, setImageSrc] = useState(null);

    const handleSave = () => {
        if (imageSrc) {
            onSave(imageSrc);  // Call the onSave function passed from the parent
            onClose();
        }
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50">
            <div className="bg-white rounded-lg p-6 pb-14 shadow-xl relative w-[400px]">
                <h2 className="text-xl font-semibold mb-4 text-center">{title}</h2>

                <ImageCapture
                    onImageCaptured={(image) => setImageSrc(image)}
                    onImageUploaded={(image) => setImageSrc(image)}
                />

                {imageSrc && (
                    <button
                        onClick={handleSave}
                        className="mt-4 bg-blue-600 text-white py-2 px-6 rounded shadow-md hover:bg-blue-700 transition"
                    >
                        Save Changes
                    </button>
                )}

                {/* Using the Button component for the close button */}
                <div className="absolute top-4 right-4">
                    <Button
                        type="close"
                        onClick={onClose}
                        label={<span className="text-xl">&times;</span>}
                        rounded={true}
                    />
                </div>
            </div>
        </div>
    );
};

export default Modal;
