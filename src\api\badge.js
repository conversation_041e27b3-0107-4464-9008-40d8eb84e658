import api from "./";

/**
 * Badge Template API Functions
 * Handles all badge template related API calls with multipart form data support
 */

// ################### Badge Template API's ##################

/**
 * Create a new badge template
 * @param {object} templateData - Template data including name, schema, key, format, content
 * @returns {Promise<any>} Response data with created template
 */
export const createBadgeTemplate = async (templateData) => {
  const payload = {
    name: templateData.name,
    schema: templateData.schema,
    key: templateData.key,
    format: templateData.format,
    content: templateData.content || {
      canvasConfig: {},
      elements: []
    },
    status: true
  };

  const response = await api.post("badges", payload);
  return response.data;
};

/**
 * Get paginated list of badge templates
 * @param {object} params - Query parameters (search, page, limit, sortBy, sortOrder)
 * @returns {Promise<any>} Response data with templates list
 */
export const getBadgeTemplates = async (params = {}) => {
  const query = {};
  if (params.search) query.search = params.search;
  if (params.page) query.page = params.page;
  if (params.limit) query.limit = params.limit;
  if (params.sortBy) query.sortBy = params.sortBy;
  if (params.sortOrder) query.sortOrder = params.sortOrder;

  const response = await api.get("badges", { params: query });
  return response.data;
};

/**
 * Get badge template by ID
 * @param {string} templateId - Template ID
 * @returns {Promise<any>} Response data with template details
 */
export const getBadgeTemplateById = async (templateId) => {
  const response = await api.get(`badges/${templateId}`);
  return response.data;
};

/**
 * Update badge template with multipart form data support
 * @param {string} templateId - Template ID
 * @param {object} templateData - Template data to update
 * @param {Array} images - Array of image files to upload
 * @returns {Promise<any>} Response data with updated template
 */
export const updateBadgeTemplate = async (templateId, templateData, images = []) => {
  // If there are images to upload, use multipart form data
  if (images && images.length > 0) {
    const formData = new FormData();

    // Add template data fields directly to form data (not as JSON string)
    if (templateData.name) formData.append('name', templateData.name);
    if (templateData.status !== undefined) formData.append('status', templateData.status);

    // Add content as JSON string
    const content = templateData.content || {
      canvasConfig: templateData.canvasConfig || {},
      elements: templateData.elements || []
    };
    formData.append('content', JSON.stringify(content));

    // Add variables as JSON string
    if (templateData.variables) {
      formData.append('variables', JSON.stringify(templateData.variables));
    }

    // Add images
    images.forEach((imageData, index) => {
      if (imageData.file) {
        formData.append(`images`, imageData.file);
      }
    });

    const response = await api.patch(`badges/${templateId}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } else {
    // No images, use regular JSON payload
    const payload = {
      name: templateData.name,
      content: templateData.content || {
        canvasConfig: templateData.canvasConfig || {},
        elements: templateData.elements || []
      },
      variables: templateData.variables || []
    };

    const response = await api.patch(`badges/${templateId}`, payload);
    return response.data;
  }
};
/**
 * Update badge template meta data
 * @param {string} templateId - Template ID
 * @param {object} templateData - Template data to update
 * @returns {Promise<any>} Response data with updated template
 */
export const updateBadgeTemplateMeta = async (templateId, templateData) => {
    const payload = {
      name: templateData.name,
      schema: templateData.schema,
      key: templateData.key,
      format: templateData.format,
    };

    const response = await api.patch(`badges/${templateId}`, payload);
    return response.data;
};

/**
 * Delete badge template
 * @param {string} templateId - Template ID
 * @returns {Promise<any>} Response data
 */
export const deleteBadgeTemplate = async (templateId) => {
  const response = await api.delete(`badges/${templateId}`);
  return response.data;
};

// ################### Schema API's ##################

/**
 * Get available schemas
 * @param {object} params - Query parameters
 * @returns {Promise<any>} Response data with schemas list
 */
export const getSchemas = async (params = {}) => {
  const response = await api.get("badges/schemas", { params });
  return response.data;
};

/**
 * Get schema keys by schema name
 * @param {string} schemaName - Schema name
 * @param {object} params - Query parameters
 * @returns {Promise<any>} Response data with keys list
 */
export const getSchemaKeys = async (schemaName, params = {}) => {
  const response = await api.get(`badges/model-attributes/${schemaName}`, { params });
  return response.data;
};

/**
 * Get schema variables by schema name
 * @param {string} schemaName - Schema name
 * @param {object} params - Query parameters
 * @returns {Promise<any>} Response data with variables list
 */
export const getSchemaVariables = async (schemaName, params = {}) => {
  const response = await api.get(`badges/model-attributes/${schemaName}`, { params });
  return response.data;
};

// ################### Badge Generation API's ##################

/**
 * Generate badge for instance
 * @param {object} data - Badge generation data
 * @returns {Promise<any>} Response data with badge image
 */
export const generateBadge = async (data) => {
  const response = await api.post('badges/print', data);
  return response.data;
};


