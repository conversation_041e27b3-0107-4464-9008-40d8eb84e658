import React, { useState } from 'react';
import Ptienthubdemo from '../../../Images/Ptienthubdemo.svg';
import PatientCard from '../../../Components/Global/PatientCard';
import EditPhotoModal from '../../../Components/Global/ImageAndCamera/EditPhotoModal';
import { IoIosArrowBack } from 'react-icons/io';
import { useNavigate } from 'react-router-dom';
import userImg from "../../../Images/fromimg.svg";

import HistoryTable from '../../../Components/Observation/HistoryTable';
import UserDemographic from '../../../Components/User/UserDemographic';
import UserGroup from '../../../Components/User/UserGroup';
import DetailsCard from '../../../Components/Global/DetailsCard';

const UserDetails = () => {

  

  const [selectedTab, setSelectedTab] = useState('Demographic Information');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const navigate = useNavigate();
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  // Function to handle image capture from the modal
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  // Create the data object expected by PatientCard
  const patientData = {
    name: "Erika",
    image: profileImage || Ptienthubdemo,
    id: "MRN333433",
    site: "India Elnath Bldg 5",
    suite: "Inpatient",
    room: "Admitted",
    bed: "N/A",
    confidential: "Yes",
    physician: "Dr. Smith",
    maxVisitors: "2"
  };

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Patient Details Card */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        handleHistoryOpen={handleHistoryOpen}
        profileImage={profileImage}
        defaultImage={userImg}
        name="Jon Smit"
        showHistoryButton={true}
        additionalFields={[
          { label: "Address", value: "123 Main St" },
          { label: "Status", value: "Active" },
          { label: "Phone number", value: "+91 ************" },
        ]}
      />
      {/* Sidebar and Main Content */}
      <div className="flex">
        {/* Sidebar */}
        <div className="w-2/12 mt-4">
          {['Demographic Information', 'User Group'].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${
                selectedTab === tab
                  ? 'text-[#4F2683] border-l-2 border-[#4F2683]'
                  : 'text-gray-700'
              }`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%]">
          {selectedTab === 'Demographic Information' && <UserDemographic />}
          {selectedTab === 'User Group' && <UserGroup />}
        </div>
      </div>

      {/* History Panel */}
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
      />

      {/* Edit Photo Modal */}
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default UserDetails;
