import api from "./";

/**
 * Logs in a user with the provided email and password.
 *
 * @param {string} email - The email address of the user.
 * @param {string} password - The password of the user.
 * @returns {Promise<any>} A promise that resolves to the response data containing authentication tokens or user details.
 */
export const login = async (email, password) => {
  const response = await api.post("auth/login", { email, password });
  return response.data;
};

/**
 * Refreshes authentication tokens using the provided refresh token.
 *
 * @param {string} refreshToken - The refresh token used to obtain new access tokens.
 * @returns {Promise<any>} A promise that resolves to the response data containing the new authentication tokens.
 */
export const refresh = async (refreshToken) => {
  const response = await api.post("auth/refresh-tokens", { refreshToken });
  return response.data;
};

/**
 * Logs out a user by invalidating the provided refresh token.
 *
 * @param {string} refreshToken - The refresh token to be invalidated.
 * @returns {Promise<any>} A promise that resolves to the response data confirming the logout operation.
 */
export const logout = async (refreshToken) => {
  const response = await api.post("auth/logout", { refreshToken });
  return response.data;
};