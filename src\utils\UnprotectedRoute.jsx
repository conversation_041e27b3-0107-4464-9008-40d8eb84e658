import React from 'react';
import { useSelector } from 'react-redux';
import { Navigate, Outlet } from 'react-router-dom';

const UnprotectedRoute = ({ redirectPath = '/Login' }) => {
  const { tokens } = useSelector((state) => state.auth);
  if (tokens && tokens.access && tokens.access.token) {
    return <Navigate to={redirectPath} replace />;
  }

  return <Outlet />;
};

export default UnprotectedRoute;
