/**
 * Image Manager Utility
 * Handles image file management for ID card designer
 * Converts from base64 to file handling with multipart uploads
 */

// Images are now handled directly through badge update endpoint

// Store for managing image files and their metadata
class ImageManager {
  constructor() {
    this.imageFiles = new Map(); // Map of element_id -> file data
    this.imageUrls = new Map();  // Map of element_id -> URL
    this.pendingUploads = new Map(); // Map of element_id -> upload promise
  }

  /**
   * Convert base64 data URL to File object
   * @param {string} dataUrl - Base64 data URL
   * @param {string} filename - Filename for the file
   * @returns {File} File object
   */
  dataURLToFile(dataUrl, filename = 'image.jpg') {
    const arr = dataUrl.split(',');
    const mime = arr[0].match(/:(.*?);/)[1];
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);
    while (n--) {
      u8arr[n] = bstr.charCodeAt(n);
    }
    return new File([u8arr], filename, { type: mime });
  }

  /**
   * Add an image file for an element
   * @param {string} elementId - Element ID
   * @param {File|string} imageData - File object or base64 data URL
   * @param {string} imageType - Type of image (static, dynamic, photo, etc.)
   * @returns {string} Temporary URL for immediate display
   */
  addImage(elementId, imageData, imageType = 'static') {
    let file;
    let tempUrl;

    if (typeof imageData === 'string' && imageData.startsWith('data:')) {
      // Convert base64 to file
      file = this.dataURLToFile(imageData, `element_${elementId}_image.jpg`);
      tempUrl = imageData; // Use base64 for immediate display
    } else if (imageData instanceof File) {
      file = imageData;
      tempUrl = URL.createObjectURL(file);
    } else {
      throw new Error('Invalid image data format');
    }

    // Store file and metadata
    this.imageFiles.set(elementId, {
      file,
      imageType,
      tempUrl,
      uploaded: false
    });

    this.imageUrls.set(elementId, tempUrl);
    return tempUrl;
  }

  /**
   * Get image URL for an element
   * @param {string} elementId - Element ID
   * @returns {string|null} Image URL or null if not found
   */
  getImageUrl(elementId) {
    return this.imageUrls.get(elementId) || null;
  }

  /**
   * Remove image for an element
   * @param {string} elementId - Element ID
   */
  removeImage(elementId) {
    const imageData = this.imageFiles.get(elementId);
    if (imageData && imageData.tempUrl && imageData.tempUrl.startsWith('blob:')) {
      URL.revokeObjectURL(imageData.tempUrl);
    }
    
    this.imageFiles.delete(elementId);
    this.imageUrls.delete(elementId);
    this.pendingUploads.delete(elementId);
  }

  /**
   * Get all pending image uploads
   * @returns {Array} Array of image data for upload
   */
  getPendingUploads() {
    const uploads = [];
    for (const [elementId, imageData] of this.imageFiles.entries()) {
      if (!imageData.uploaded) {
        uploads.push({
          element_id: elementId,
          file: imageData.file,
          image_type: imageData.imageType
        });
      }
    }
    return uploads;
  }

  /**
   * Get all images for upload (used by badge update endpoint)
   * @returns {Array} Array of image data for upload
   */
  getImagesForUpload() {
    const images = [];
    for (const [elementId, imageData] of this.imageFiles.entries()) {
      if (imageData.file && !imageData.uploaded) {
        images.push({
          element_id: elementId,
          file: imageData.file,
          image_type: 'static'
        });
      }
    }
    return images;
  }

  /**
   * Mark images as uploaded (called after successful badge update)
   * @param {Array} uploadedImages - Array of uploaded image data
   */
  markImagesAsUploaded(uploadedImages) {
    for (const imageData of uploadedImages) {
      const elementId = imageData.element_id;
      const existingData = this.imageFiles.get(elementId);

      if (existingData) {
        // Clean up the blob URL
        if (existingData.tempUrl && existingData.tempUrl.startsWith('blob:')) {
          URL.revokeObjectURL(existingData.tempUrl);
        }

        // Update with uploaded URL
        this.imageFiles.set(elementId, {
          ...existingData,
          tempUrl: imageData.url || imageData.image_url,
          uploaded: true
        });

        this.imageUrls.set(elementId, imageData.url || imageData.image_url);
      }
    }
  }

  /**
   * Clear all images
   */
  clear() {
    // Clean up blob URLs
    for (const [, imageData] of this.imageFiles.entries()) {
      if (imageData.tempUrl && imageData.tempUrl.startsWith('blob:')) {
        URL.revokeObjectURL(imageData.tempUrl);
      }
    }

    this.imageFiles.clear();
    this.imageUrls.clear();
    this.pendingUploads.clear();
  }

  /**
   * Get image file for an element
   * @param {string} elementId - Element ID
   * @returns {File|null} File object or null
   */
  getImageFile(elementId) {
    const imageData = this.imageFiles.get(elementId);
    return imageData ? imageData.file : null;
  }

  /**
   * Check if element has pending upload
   * @param {string} elementId - Element ID
   * @returns {boolean} True if upload is pending
   */
  hasPendingUpload(elementId) {
    const imageData = this.imageFiles.get(elementId);
    return imageData && !imageData.uploaded;
  }

  /**
   * Get upload status for all images
   * @returns {Object} Status object with counts
   */
  getUploadStatus() {
    let total = 0;
    let uploaded = 0;
    let pending = 0;

    for (const [, imageData] of this.imageFiles.entries()) {
      total++;
      if (imageData.uploaded) {
        uploaded++;
      } else {
        pending++;
      }
    }

    return { total, uploaded, pending };
  }
}

// Create singleton instance
const imageManager = new ImageManager();

export default imageManager;

// Export utility functions
export const {
  addImage,
  getImageUrl,
  removeImage,
  uploadImage,
  uploadAllImages,
  clear,
  getImageFile,
  hasPendingUpload,
  getUploadStatus,
  getPendingUploads
} = imageManager;
