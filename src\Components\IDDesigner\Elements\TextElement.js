import React, { forwardRef } from 'react';
import { Text, Group, Rect } from 'react-konva';
import { useSelector } from 'react-redux';
import Mustache from 'mustache';

const TextElement = forwardRef(({
  element,
  isPreviewMode,
  onClick,
  onDragEnd,
}, ref) => {
  const { mockData } = useSelector((state) => state.idDesigner);

  const getDisplayText = () => {
    if (isPreviewMode && element.text) {
      try {
        // Render mustache template with mock data
        return Mustache.render(element.text, mockData);
      } catch (error) {
        console.warn('Error rendering mustache template:', error);
        return element.text;
      }
    }

    // In design mode, show variables as styled chips
    if (!isPreviewMode && element.text) {
      return element.text; // We'll handle variable styling separately
    }

    return element.text || 'Text';
  };

  // Parse text to identify variables for chip-like display
  const parseTextForChips = (text) => {
    if (!text || isPreviewMode) return null;

    const parts = [];
    const regex = /\{\{([^}]+)\}\}/g;
    let lastIndex = 0;
    let match;

    while ((match = regex.exec(text)) !== null) {
      // Add text before the variable
      if (match.index > lastIndex) {
        parts.push({
          type: 'text',
          content: text.slice(lastIndex, match.index),
          start: lastIndex,
          end: match.index
        });
      }

      // Add the variable
      parts.push({
        type: 'variable',
        content: match[1],
        fullMatch: match[0],
        start: match.index,
        end: match.index + match[0].length
      });

      lastIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (lastIndex < text.length) {
      parts.push({
        type: 'text',
        content: text.slice(lastIndex),
        start: lastIndex,
        end: text.length
      });
    }

    return parts.length > 1 ? parts : null;
  };



  // Auto-resize font based on element dimensions
  const getAutoFontSize = () => {
    // If autoResize is explicitly disabled, always use the set fontSize
    if (element.autoResize === false) {
      return element.fontSize || 16;
    }

    // If autoResize is enabled or not set, calculate based on dimensions
    if (element.autoResize === true || element.autoResize === undefined) {
      const baseSize = Math.min(element.width, element.height) / 4;
      return Math.max(8, Math.min(baseSize, element.fontSize || 16));
    }

    // Default fallback
    return element.fontSize || 16;
  };

  const fontSize = getAutoFontSize();
  const textParts = parseTextForChips(element.text);

  // If we have variables to display as chips and not in preview mode
  if (textParts && !isPreviewMode) {
    return (
      <Group
        ref={ref}
        x={element.x}
        y={element.y}
        width={element.width}
        height={element.height}
        rotation={element.rotation || 0}
        opacity={element.opacity !== undefined ? element.opacity : 1}
        visible={element.visible !== false}
        draggable={!element.locked && !isPreviewMode}
        onClick={onClick}
        onDragEnd={onDragEnd}
      >
        {/* Background for the text area */}
        <Rect
          width={element.width}
          height={element.height}
          fill="transparent"
          stroke="rgba(79, 38, 131, 0.2)"
          strokeWidth={1}
          dash={[5, 5]}
        />

        {/* Render text parts with variable chips */}
        {textParts.map((part, index) => {
          const yOffset = 10 + (index * (fontSize + 8)); // Stack parts vertically

          if (part.type === 'variable') {
            return (
              <Group key={index} x={10} y={yOffset}>
                {/* Variable chip background */}
                <Rect
                  width={part.content.length * (fontSize * 0.6) + 20}
                  height={fontSize + 8}
                  fill="rgba(79, 38, 131, 0.1)"
                  stroke="#4f2683"
                  strokeWidth={1}
                  cornerRadius={12}
                />
                {/* Variable chip text */}
                <Text
                  x={10}
                  y={4}
                  text={`{{${part.content}}}`}
                  fontSize={fontSize * 0.8}
                  fontFamily="monospace"
                  fill="#4f2683"
                  fontStyle="bold"
                />
              </Group>
            );
          } else {
            return (
              <Text
                key={index}
                x={10}
                y={yOffset}
                text={part.content}
                fontSize={fontSize}
                fontFamily={element.fontFamily || 'Arial'}
                fontStyle={element.fontStyle || 'normal'}
                fill={element.color || '#000000'}
                width={element.width - 20}
                wrap="word"
              />
            );
          }
        })}
      </Group>
    );
  }

  // Default text rendering for preview mode or simple text
  const textProps = {
    ref,
    x: element.x,
    y: element.y,
    width: element.width,
    height: element.height,
    text: getDisplayText(),
    fontSize: fontSize,
    fontFamily: element.fontFamily || 'Arial',
    fontStyle: element.fontStyle || 'normal',
    fontVariant: element.fontVariant || 'normal',
    fill: element.color || '#000000',
    align: element.textAlign || 'center',
    verticalAlign: element.verticalAlign || 'middle',
    wrap: element.wrap || 'word',
    ellipsis: element.ellipsis || false,
    lineHeight: element.lineHeight || 1.2,
    rotation: element.rotation || 0,
    opacity: element.opacity !== undefined ? element.opacity : 1,
    visible: element.visible !== false,
    draggable: !element.locked && !isPreviewMode,
    onClick,
    onDragEnd,
    offsetX: 0,
    offsetY: 0,
  };

  // Add text decoration styles
  if (element.textDecoration) {
    if (element.textDecoration.includes('underline')) {
      textProps.textDecoration = 'underline';
    }
    if (element.textDecoration.includes('line-through')) {
      textProps.textDecoration = 'line-through';
    }
  }

  // Add shadow if specified
  if (element.shadow) {
    textProps.shadowColor = element.shadow.color || 'rgba(0,0,0,0.5)';
    textProps.shadowBlur = element.shadow.blur || 5;
    textProps.shadowOffset = element.shadow.offset || { x: 2, y: 2 };
    textProps.shadowOpacity = element.shadow.opacity || 0.5;
  }

  return <Text {...textProps} />;
});

TextElement.displayName = 'TextElement';

export default TextElement;
