import React, { useState, useRef, useEffect } from "react";

const SearchableDropdown = ({
  options = [],
  value = "",
  onSelect,
  placeholder = "Select an option",
  bgColor = "bg-white",
  textColor = "text-black",
  hoverBgColor = "hover:bg-gray-200",
  borderColor = "border-gray-300",
  className = "",
  rounded = "rounded-md",
}) => {
  const [searchTerm, setSearchTerm] = useState(value);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  const filteredOptions = options.filter((option) =>
    option.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelect = (option) => {
    setSearchTerm(option);
    onSelect(option);
    setIsOpen(false);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <input
        type="text"
        value={searchTerm}
        onChange={(e) => {
          setSearchTerm(e.target.value);
          setIsOpen(true);
        }}
        onFocus={() => setIsOpen(true)}
        placeholder={placeholder}
        className={`w-full ${bgColor} ${borderColor} border ${rounded} p-2 ${textColor}`}
      />
      {isOpen && (
        <div className={`absolute mt-1 w-full ${bgColor} ${borderColor} border ${rounded} shadow-lg z-10`}>
          {filteredOptions.length > 0 ? (
            filteredOptions.map((option, index) => (
              <div
                key={index}
                onClick={() => handleSelect(option)}
                className={`p-2 cursor-pointer hover:text-white ${hoverBgColor}`}
              >
                {option}
              </div>
            ))
          ) : (
            <div className="p-2 text-gray-500">No Results Found</div>
          )}
        </div>
      )}
    </div>
  );
};

export default SearchableDropdown;
