.react-datepicker__day,
.react-datepicker__month-text {
  color: #4F2683 !important;
}

.react-datepicker__day--today {
  background-color: #ab7be9 !important;
  color: white !important;
  border-radius: 50%;
}

.react-datepicker__day--selected {
  background-color: #4F2683 !important;
  color: white !important;
}

.react-datepicker-wrapper {
  width: 100%;
}

/* Updated layout: use flex to align date and time side by side */
.react-datepicker {

  display: flex;
  flex-direction: row;
  position: relative;
  /* top: 50%; */
  /* left: 50%; */
  transform: translate(0%, 0%);
  /* z-index: ; */
}

/* Ensure the month (calendar) and time sections share space appropriately */
.react-datepicker__month-container,
.react-datepicker__time-container {
  width: auto;
}

/* Add a separator and some padding to the time container */
.react-datepicker__time-container {
  border-left: 1px solid #4F2683;
}

/* Existing dropdown styles remain unchanged */
.react-datepicker__year-dropdown {
  height: 200px;
}

.react-datepicker__month-dropdown,
.react-datepicker__year-dropdown {
  position: relative;
  background-color: #fff;
  border: 1px solid #4F2683;
  border-radius: 6px;
  box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
  padding: 2px 3px;
}

.react-datepicker__month-option,
.react-datepicker__year-option {
  padding: 4px 16px;
  cursor: pointer;
  color: #4F2683;
  font-weight: 400;
}

.react-datepicker__month-option:hover,
.react-datepicker__year-option:hover {
  background-color: #4F2683;
  color: white;
}

.react-datepicker__month-option--selected,
.react-datepicker__year-option--selected {
  background-color: #4F2683 !important;
  color: white !important;
  padding: 0 1px;
}
