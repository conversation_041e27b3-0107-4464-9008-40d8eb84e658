import React, { useState, useEffect } from "react";
import GenericTable from "../../GenericTable";
import AddAccessForm from "./AddAccessForm";
import ViewEditAccessForm from "./ViewEditAccessAreaForm";
import deleted from "../../../Images/Delete.svg";
import PrintIcon from "../../../Images/print.svg";
import TruncatedCell from "../../Tooltip/TruncatedCell";
import { deleteIdentityAccess, getIdentityAccessByIdentity } from "../../../api/identity"; // <-- Import API
import { useLocation } from "react-router-dom";
import { useIdentityData } from "../../../hooks/useIdentityData";

const AccessArea = () => {
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id"); // Fetch identity_id from query parameters
  const [searchTerm, setSearchTerm] = useState("");
  const [accessAreas, setAccessAreas] = useState([]);
  const [showAddAccessForm, setShowAddAccessForm] = useState(false);
  const [showViewAccessForm, setShowViewAccessForm] = useState(false);
  const [selectedAccess, setSelectedAccess] = useState(null);
  const { accessStatusOptions, accessStatusMap } = useIdentityData();

  // Fetch access areas from API on mount
// In fetchAccessAreas useEffect (AccessArea.jsx)
useEffect(() => {
  const fetchAccessAreas = async () => {
    try {
      const params = { 
        search: searchTerm || undefined,
      };
      const res = await getIdentityAccessByIdentity(identityId , params);
      console.log(res);
      setAccessAreas(
        (res.data || []).map(item => ({
          ...item, // Preserve original fields
          id: item.identity_access_id, // Use actual ID from API
          areaName: item.access_level_name,
          cardNumber: item.card_number,
          startDate: item.start_date?.split("T")[0] || "",
          endDate: item.end_date?.split("T")[0] || "",
          status: accessStatusMap?.[item.status] || item.status
        }))
      );
    } catch (error) {
      setAccessAreas([]);
    }
  };
  fetchAccessAreas();
}, [identityId, accessStatusMap ,searchTerm]);

  // Open the add form modal
  const handleAdd = () => {
    setShowAddAccessForm(true);
  };


  // Print record handler
  const handlePrint = (access) => {
    // Implement your print functionality here
    console.log("Printing access details:", access);
  };

  // View record handler
  const handleView = (access) => {
    setSelectedAccess(access);
    setShowViewAccessForm(true);
  };

  // Update record handler
// In AccessArea component
const handleUpdate = (updatedData) => {
  setAccessAreas(prev => 
    prev.map(item => 
      item.identity_access_id === updatedData.identity_access_id 
        ? {...item, ...updatedData} 
        : item
    )
  );
  setShowViewAccessForm(false);
};


  const handleDelete = async (access) => {
  const confirmed = window.confirm("Are you sure you want to delete this access record?");
  if (!confirmed) return;

  try {
    await deleteIdentityAccess(access.identity_access_id); // Assuming this is the correct ID field
    setAccessAreas((prev) => prev.filter((item) => item.identity_access_id !== access.identity_access_id));
  } catch (error) {
    console.error("Failed to delete access record:", error);
    alert("Failed to delete. Please try again.");
  }
};

  // Define table columns
  const columns = [
    {
      name: "Area Name",
      selector: (row) => row.areaName,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.areaName}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Card Number",
      selector: (row) => row.cardNumber,
      cell: (row) => <TruncatedCell text={row.cardNumber} />,
    },
    {
      name: "Start Date",
      selector: (row) => row.startDate,
    },
    {
      name: "End Date",
      selector: (row) => row.endDate,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-24 py-1 flex justify-center items-center text-sm rounded-full ${
            row.status === (accessStatusMap && accessStatusMap[0] ? accessStatusMap[0] : "Active")
             ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <div className="flex space-x-2">
          <img
            src={PrintIcon}
            alt="Print"
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
            onClick={() => handlePrint(row)}
          />
          <img
            src={deleted}
            alt="deleted"
            onClick={() => handleDelete(row)}
            className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5]"
          />
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ];

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Access Areas"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAdd}
        columns={columns}
        data={accessAreas}
        showSearch={true}
        showAddButton={true}
      />

      {showAddAccessForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddAccessForm
                onClose={() => setShowAddAccessForm(false)}
               onSubmit={(newAccess) => {
    setAccessAreas(prev => [{
      ...newAccess,
      id: newAccess.identity_access_id,
      areaName: newAccess.access_level_name,
      cardNumber: newAccess.card_number,
      startDate: newAccess.start_date?.split("T")[0] || "",
      endDate: newAccess.end_date?.split("T")[0] || "",
      status: accessStatusMap?.[newAccess.status] || newAccess.status
    }, ...prev]);
    setShowAddAccessForm(false);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {showViewAccessForm &&selectedAccess &&  (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditAccessForm
                accessData={selectedAccess}
                onUpdate={handleUpdate}
                onClose={() => setShowViewAccessForm(false)}
             
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccessArea;
