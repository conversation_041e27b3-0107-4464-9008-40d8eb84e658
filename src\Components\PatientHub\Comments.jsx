// import React, { useState } from 'react';
// import GenericTable from "../GenericTable";
// import AddComments from './AddComments';
// import ViewCommentsEditModal from './ViewCommentsEditModal';

// const Comments = () => {
//   const [data, setData] = useState([
//     { id: 1, comment: 'Great service!', addedBy: 'Alice', addedOn: '2025-01-01' },
//     { id: 2, comment: 'Loved it!', addedBy: 'Bob', addedOn: '2025-01-02' },
//     { id: 3, comment: 'Not bad.', addedBy: 'Charlie', addedOn: '2025-01-03' },
//     { id: 4, comment: 'Could be better.', addedBy: '<PERSON>', addedOn: '2025-01-04' },
//     { id: 5, comment: 'Excellent!', addedBy: 'Evan', addedOn: '2025-01-05' },
//   ]);

//   const [selectedRow, setSelectedRow] = useState(null);
//   const [isViewModalOpen, setViewModalOpen] = useState(false);
//   const [isAddModalOpen, setAddModalOpen] = useState(false);
//   const [newEntry, setNewEntry] = useState({ comment: '', addedBy: '', addedOn: '' });

//   const handleRowClick = (row) => {
//     setSelectedRow(row);
//     setViewModalOpen(true);
//   };

//   // Update the parent's data with the edited information from the modal
//   const handleUpdate = (updatedEntry) => {
//     setData(data.map((item) => (item.id === updatedEntry.id ? updatedEntry : item)));
//     setSelectedRow(updatedEntry);
//     setViewModalOpen(false);
//   };

//   const handleAddClick = () => {
//     setNewEntry({ comment: '', addedBy: '', addedOn: '' });
//     setAddModalOpen(true);
//   };

//   const handleSave = () => {
//     setData([{ id: data.length + 1, ...newEntry }, ...data]);
//     setAddModalOpen(false);
//   };

 
//   const columns = [
//     {
//       name: 'Comments',
//       selector: (row) => row.comment,
//       sortable: true,
//       cell: (row) => (
//         <span className='underline cursor-pointer' onClick={() => handleRowClick(row)}>
//           {row.comment}
//         </span>
//       ),
//     },
//     {
//       name: 'Added by',
//       selector: (row) => row.addedBy,
//       sortable: true,
//     },
//     {
//       name: 'Added On',
//       selector: (row) => row.addedOn,
//       sortable: true,
//     },
   
//   ];

//   return (
//     <div>
//       <div className='bg-white rounded-[10px]'>
//         <GenericTable
//           title="Comments"
//           showSearch={true}
//           onAdd={handleAddClick}
//           columns={columns}
//           data={data}
//           showAddButton={true}
//         />
//       </div>

//       {/* View/Edit Modal */}
//       {isViewModalOpen && (
//         <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
//           <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
//             <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
//               <ViewCommentsEditModal
//                 commentData={selectedRow}
//                 onUpdate={handleUpdate}
//                 onClose={() => setViewModalOpen(false)}
//               />
//             </div>
//           </div>
//         </div>
//       )}

//       {/* Add Modal */}
//       {isAddModalOpen && (
//         <AddComments
//           newEntry={newEntry}
//           setNewEntry={setNewEntry}
//           onSave={handleSave}
//           onClose={() => setAddModalOpen(false)}
//         />
//       )}
//     </div>
//   );
// };

// export default Comments;
