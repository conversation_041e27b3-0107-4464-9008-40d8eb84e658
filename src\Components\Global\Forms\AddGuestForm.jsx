// AddGuestForm.js
import React, { useState } from 'react';

const AddGuestForm = ({ onSave, onClose }) => {
  const [guestName, setGuestName] = useState('');
  const [screening, setScreening] = useState(false);
  const [arrivalTime, setArrivalTime] = useState('');

  const handleSubmit = (e) => {
    e.preventDefault();
    if (guestName && arrivalTime) {
      onSave({ guestName, screening, arrivalTime });
      setGuestName('');
      setArrivalTime('');
      onClose();
    } else {
      alert('Please fill in all fields.');
    }
  };

  return (
    <div className="bg-white p-4 rounded shadow-md">
      <h3 className="text-lg font-semibold mb-4">Add New Guest</h3>
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block mb-1">Guest Name:</label>
          <input
            type="text"
            value={guestName}
            onChange={(e) => setGuestName(e.target.value)}
            className="border rounded w-full p-2"
            required
          />
        </div>
        <div className="mb-4">
          <label className="block mb-1">Arrival Time:</label>
          <input
            type="time"
            value={arrivalTime}
            onChange={(e) => setArrivalTime(e.target.value)}
            className="border rounded w-full p-2"
            required
          />
        </div>
        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={screening}
              onChange={(e) => setScreening(e.target.checked)}
              className="mr-2"
            />
            Screening Completed
          </label>
        </div>
        <button type="submit" className="bg-blue-500 text-white px-4 py-2 rounded">
          Add Guest
        </button>
        <button type="button" onClick={onClose} className="ml-2 bg-gray-300 text-black px-4 py-2 rounded">
          Cancel
        </button>
      </form>
    </div>
  );
};

export default AddGuestForm;
