import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { login as loginAPI, logout as logoutAPI } from '../api/auth';

// Load saved state from localStorage
const savedTokens = localStorage.getItem('tokens') ? JSON.parse(localStorage.getItem('tokens')) : null;
const savedIdentity = localStorage.getItem('identity') ? JSON.parse(localStorage.getItem('identity')) : null;

const initialState = {
  identity: savedIdentity,
  tokens: savedTokens,
  loading: false,
  error: null,
};

// Login thunk
export const login = createAsyncThunk(
  'auth/login',
  async ({ email, password }, thunkAPI) => {
    try {
      const request = await loginAPI(email, password);
      return request.data;
    } catch (error) {
      const message =
        error.response && error.response.data
          ? typeof error.response.data === 'object'
            ? JSON.stringify(error.response.data)
            : error.response.data
          : error.message || 'Lo<PERSON> failed';
      return thunkAPI.rejectWithValue(message);
    }
  }
);

// Logout thunk
export const logoutUser = createAsyncThunk(
  'auth/logout',
  async (_, thunkAPI) => {
    try {
      const state = thunkAPI.getState();
      const currentRefreshToken = state.auth.tokens?.refresh?.token;
      await logoutAPI(currentRefreshToken);
      // await logoutAPI(); // Call the logout API if available
      return;
    } catch (error) {
      return thunkAPI.rejectWithValue(error.message);
    }
  }
);

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    logout(state) {
      state.identity = null;
      state.tokens = null;
      localStorage.removeItem('tokens');
      localStorage.removeItem('identity');
    },
  },
  extraReducers: (builder) => {
    builder
      // LOGIN cases
      .addCase(login.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(login.fulfilled, (state, action) => {
        state.loading = false;
        state.identity = action.payload.identity;
        state.tokens = action.payload.tokens;
        // Persist auth data to localStorage
        localStorage.setItem('tokens', JSON.stringify(action.payload.tokens));
        localStorage.setItem('identity', JSON.stringify(action.payload.identity));
      })
      .addCase(login.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
      })
      // LOGOUT cases
      .addCase(logoutUser.fulfilled, (state) => {
        state.identity = null;
        state.tokens = null;
        localStorage.removeItem('tokens');
        localStorage.removeItem('identity');
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.error = action.payload;
      });
  },
});

export const { logout } = authSlice.actions;
export default authSlice.reducer;
