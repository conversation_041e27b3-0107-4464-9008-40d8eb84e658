import React, { useState } from "react";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";

const ViewEditInterfaceStatusForm = ({ statusData, onUpdate, onClose }) => {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    id: statusData.id || "",
    referenceId: statusData.referenceId || "",
    source: statusData.source || "",
    destination: statusData.destination || "",
    sentDate: statusData.sentDate || "",
    ackDate: statusData.ackDate || "",
    status: statusData.status || "",
  });
  const [show, setShow] = useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add Document</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        <form onSubmit={handleSave} className="p-6">
          {/* Reference ID */}
          <div className="flex items-center mb-4">
            <label
              htmlFor="referenceId"
              className="w-1/4 text-[16px] font-normal"
            >
              Reference ID
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="referenceId"
                id="referenceId"
                value={formData.referenceId}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Source */}
          <div className="flex items-center mb-4">
            <label htmlFor="source" className="w-1/4 text-[16px] font-normal">
              Source
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="source"
                id="source"
                value={formData.source}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Destination */}
          <div className="flex items-center mb-4">
            <label htmlFor="destination" className="w-1/4 text-[16px] font-normal">
              Destination
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="destination"
                id="destination"
                value={formData.destination}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Sent Date */}
          <div className="flex items-center mb-4">
            <label htmlFor="sentDate" className="w-1/4 text-[16px] font-normal">
              Sent Date
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                name="sentDate"
                id="sentDate"
                value={formData.sentDate}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Ack Date */}
          <div className="flex items-center mb-4">
            <label htmlFor="ackDate" className="w-1/4 text-[16px] font-normal">
              Ack Date
            </label>
            <div className="w-3/4">
              <Input
                type="date"
                name="ackDate"
                id="ackDate"
                value={formData.ackDate}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>
          {/* Status */}
          <div className="flex items-center mb-4">
            <label htmlFor="status" className="w-1/4 text-[16px] font-normal">
              Status
            </label>
            <div className="w-3/4">
              {isEditMode ? (
                <CustomDropdown
                  className="h-11 rounded border-gray-300"
                  options={["Active", "Inactive"]}
                  onSelect={(option) =>
                    setFormData({ ...formData, status: option })
                  }
                  selectedOption={formData.status}
                  value={formData.status}
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              ) : (
                <Input
                  type="text"
                  name="status"
                  id="status"
                  value={formData.status}
                  disabled
                  className={inputClassName}
                />
              )}
            </div>
          </div>
          <div className="flex gap-4 justify-end">
            {!isEditMode ? (
              <button
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  setIsEditMode(true);
                }}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditMode(false);
                    setFormData({
                      id: statusData.id || "",
                      referenceId: statusData.referenceId || "",
                      source: statusData.source || "",
                      destination: statusData.destination || "",
                      sentDate: statusData.sentDate || "",
                      ackDate: statusData.ackDate || "",
                      status: statusData.status || "",
                    });
                  }}
                  className="px-4 py-2 bg-gray-400 text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewEditInterfaceStatusForm;
