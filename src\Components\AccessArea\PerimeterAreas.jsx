import React, { useState } from "react";
import GenericTable from "../GenericTable";
import PerimeterAreasAdd from "./PerimeterAreasAdd";
import Delete from "../../Images/Delete.svg";


const PerimeterAreas = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [areas, setAreas] = useState([
    { id: 1, areaName: "Area 101", areaType: "Office", system: "HVAC" },
    { id: 2, areaName: "Area 102", areaType: "Conference", system: "Lighting" },
    { id: 3, areaName: "Area 201", areaType: "Lab", system: "Security" },
    { id: 4, areaName: "Area 202", areaType: "Storage", system: "Fire" },
    { id: 5, areaName: "Area 301", areaType: "Office", system: "HVAC" },
    { id: 6, areaName: "Area 302", areaType: "Break Room", system: "Cafeteria" },
    { id: 7, areaName: "Area 401", areaType: "Meeting", system: "Audio" },
    { id: 8, areaName: "Area 402", areaType: "Office", system: "IT" },
  ]);

  const [showAddAreaForm, setShowAddAreaForm] = useState(false);

  const columns = [
    {
      name: "Area Name",
      selector: (row) => row.areaName,
      sortable: true,
    },
    {
      name: "Area Type",
      selector: (row) => row.areaType,
    },
    {
      name: "System",
      selector: (row) => row.system,
    },
    {
      name: "Action",
      cell: (row) => (
        <img src={Delete} alt="Delete" 
         className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDelete(row.id)} />
      ),
    },
  ];

  const handleDelete = (id) => {
    setAreas((prevAreas) => prevAreas.filter((area) => area.id !== id));
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Perimeter Areas"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={() => setShowAddAreaForm(true)}
        columns={columns}
        data={areas}
        showSearch={true}
        showAddButton={true}
      />
      {showAddAreaForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
           <div className="bg-white shadow-lg p-1 rounded-lg">
           <div className="rounded-lg max-h-[90vh] ">
            <PerimeterAreasAdd
              onClose={() => setShowAddAreaForm(false)}
              onSubmit={(newArea, action) => {
                const areaToAdd = { ...newArea, id: Date.now() };
                setAreas([areaToAdd, ...areas]);
                if (action === "add") {
                  setShowAddAreaForm(false);
                }
              }}
            />
          </div>
          </div>

        </div>
      )}
    </div>
  );
};

export default PerimeterAreas;
