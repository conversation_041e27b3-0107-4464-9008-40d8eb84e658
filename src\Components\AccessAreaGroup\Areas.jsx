import React, { useState } from "react";
import GenericTable from "../GenericTable";
import AreasAdd from "./AreaAdd";
import { MdOutlineDeleteForever } from "react-icons/md";
import { AreasData } from "../../api/static";

const Areas = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [areas, setAreas] = useState(AreasData);

  const [showAddAreaForm, setShowAddAreaForm] = useState(false);

  const columns = [
    {
      name: "Area Name",
      selector: (row) => row.areaName,
      sortable: true,
    },
    {
      name: "Area Type",
      selector: (row) => row.areaType,
      sortable: true,
    },
    {
      name: "System",
      selector: (row) => row.system,
      sortable: true,
    },
    {
      name: "Action",
      cell: (row) => (
        <MdOutlineDeleteForever
          size={32}
          className="p-2 rounded-lg cursor-pointer bg-[#F0EDF5] text-red-600"
          onClick={() => handleDelete(row.id)}
        />
      ),
    },
  ];

  const handleDelete = (id) => {
    setAreas((prevAreas) => prevAreas.filter((area) => area.id !== id));
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Perimeter Areas"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={() => setShowAddAreaForm(true)}
        columns={columns}
        data={areas}
        showSearch={true}
        showAddButton={true}
      />
      {showAddAreaForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
           <div className="bg-white shadow-lg p-1 rounded-lg">
           <div className="rounded-lg max-h-[90vh] ">
            <AreasAdd
              onClose={() => setShowAddAreaForm(false)}
              onSubmit={(newArea, action) => {
                const areaToAdd = { ...newArea, id: Date.now() };
                setAreas([areaToAdd, ...areas]);
                if (action === "add") {
                  setShowAddAreaForm(false);
                }
              }}
            />
          </div>
          </div>

        </div>
      )}
    </div>
  );
};

export default Areas;
