import React from 'react';
import { Chip } from '@mui/material';

const VariableChip = ({ variable, onDelete, readOnly = false }) => {
  return (
    <Chip
      label={`{{${variable}}}`}
      size="small"
      variant="outlined"
      color="primary"
      onDelete={readOnly ? undefined : onDelete}
      sx={{
        backgroundColor: 'rgba(79, 38, 131, 0.1)',
        borderColor: '#4f2683',
        color: '#4f2683',
        fontFamily: 'monospace',
        fontSize: '0.75rem',
        height: '20px',
        '& .MuiChip-deleteIcon': {
          color: '#4f2683',
          fontSize: '14px',
        },
        '& .MuiChip-label': {
          paddingLeft: '6px',
          paddingRight: readOnly ? '6px' : '4px',
        },
        margin: '2px',
        cursor: readOnly ? 'default' : 'pointer',
      }}
    />
  );
};

export default VariableChip;
