import { useState, useEffect, useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import {
  Box,
  Typography,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Slider,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  IconButton,
  Chip,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Delete as DeleteIcon,
  ContentCopy as DuplicateIcon,
  Lock as LockIcon,
  LockOpen as UnlockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  RotateRight as RotateIcon,
  FormatAlignLeft as AlignLeftIcon,
  FormatAlignCenter as AlignCenterIcon,
  FormatAlignRight as AlignRightIcon,
  FormatAlignJustify as AlignJustifyIcon,
  FormatBold as BoldIcon,
  FormatItalic as ItalicIcon,
  FormatUnderlined as UnderlineIcon,
  FormatStrikethrough as StrikethroughIcon,
  CreditCard as CardIcon,
  Badge as BadgeIcon,
  Rectangle as CustomIcon,
  Visibility as PreviewIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';

import {
  updateElement,
  deleteElement,
  duplicateElement,
  moveElementToFront,
  moveElementToBack,
  updateCanvasConfig,
  setCanvasUnit,
  toggleRulers,
  setCardType,
  togglePreviewMode,
  setCurrentTemplate,
} from '../../redux/idDesignerSlice';
import { UNITS, fromPixels, toPixels, formatWithUnit, getGridSizeInPixels } from '../../utils/unitConversion';
import { getSchemaVariables, getSchemas, getSchemaKeys, updateBadgeTemplate, updateBadgeTemplateMeta } from '../../api/badge';
import { toast } from 'react-toastify';
import imageManager from '../../utils/imageManager';
import TextEditorWithVariables from './TextEditor/TextEditorWithVariables';

const PropertyPanel = ({templateMeta}) => {
  const dispatch = useDispatch();
  const { elements, selectedElementIds, canvasConfig, previewMode, currentTemplate } = useSelector((state) => state.idDesigner);
  const [availableVariables, setAvailableVariables] = useState([]);

  // Template properties state
  const [schemas, setSchemas] = useState([]);
  const [keys, setKeys] = useState([]);
  const [loadingSchemas, setLoadingSchemas] = useState(false);
  const [loadingKeys, setLoadingKeys] = useState(false);
  const [updatingTemplate, setUpdatingTemplate] = useState(false);

  const selectedElements = elements.filter(el => selectedElementIds.includes(el.id));
  const selectedElement = selectedElements.length === 1 ? selectedElements[0] : null;

  // Load schemas on component mount
  useEffect(() => {
    loadSchemas();
  }, []);

  const loadVariables = useCallback(async () => {
    if (!templateMeta.schema) return;

    try {
      const response = await getSchemaVariables(templateMeta.schema);
      setAvailableVariables(response.data || []);
    } catch (error) {
      console.error('Error loading variables:', error);
      setAvailableVariables([]);
    } finally {
    }
  }, [templateMeta.schema]);

  // Load variables when template schema is available
  useEffect(() => {
    if (templateMeta.schema) {
      loadVariables();
    }
  }, [templateMeta.schema, loadVariables]);

  // Load schemas for template properties
  const loadSchemas = async () => {
    setLoadingSchemas(true);
    try {
      const response = await getSchemas();
      setSchemas(response?.data?.schemas || []);
    } catch (error) {
      console.error('Error loading schemas:', error);
      toast.error('Failed to load schemas');
    } finally {
      setLoadingSchemas(false);
    }
  };

  // Load keys for selected schema
  const loadKeys = useCallback(async (schemaName) => {
    if (!schemaName) {
      setKeys([]);
      return;
    }

    setLoadingKeys(true);
    try {
      const response = await getSchemaKeys(schemaName);
      setKeys(response.data || []);
    } catch (error) {
      console.error('Error loading keys:', error);
      toast.error('Failed to load keys');
    } finally {
      setLoadingKeys(false);
    }
  }, []);

  // Load keys when schema changes
  useEffect(() => {
    if (templateMeta.schema) {
      loadKeys(templateMeta.schema);
    }
  }, [templateMeta.schema, loadKeys]);

  // Update template properties
  const updateTemplateProperties = async (updates) => {
    if (!currentTemplate.id) return;

    setUpdatingTemplate(true);
    try {
      // Only update template metadata, not content
      const templateData = {
        name: updates.name || templateMeta.name,
        schema: updates.schema || templateMeta.schema,
        key: updates.key || templateMeta.key,
        format: updates.format || templateMeta.format
      };

      await updateBadgeTemplateMeta(templateMeta.id, templateData);

      // Update local state
      dispatch(setCurrentTemplate(updates));

      toast.success('Template properties updated successfully');
    } catch (error) {
      console.error('Error updating template properties:', error);
      toast.error('Failed to update template properties');
    } finally {
      setUpdatingTemplate(false);
    }
  };

  // Available formats
  const formats = [
    { value: 'PNG', label: 'PNG' },
    { value: 'JPEG', label: 'JPEG' },
    { value: 'JPG', label: 'JPG' },
    { value: 'WEBP', label: 'WEBP' },
    { value: 'BMP', label: 'BMP' },
    { value: 'TIFF', label: 'TIFF' },
    { value: 'PDF', label: 'PDF' }
  ];

  // Render template properties section
  const renderTemplateProperties = () => (
    <Box sx={{ p: 2 }}>
      <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600, mb: 2 }}>
        Template Properties
      </Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
        {/* Template Name */}
        <TextField
          label="Template Name"
          value={currentTemplate.name || ''}
          onChange={(e) => updateTemplateProperties({ name: e.target.value })}
          disabled={updatingTemplate}
          size="small"
          fullWidth
        />

        {/* Schema Selection */}
        <FormControl size="small" fullWidth disabled={updatingTemplate || loadingSchemas}>
          <InputLabel>Schema</InputLabel>
          <Select
            value={templateMeta.schema || ''}
            onChange={(e) => updateTemplateProperties({ schema: e.target.value, key: '' })}
            label="Schema"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {schemas.map((schema) => (
              <MenuItem key={schema} value={schema}>
                {schema}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Key Selection */}
        <FormControl size="small" fullWidth disabled={updatingTemplate || loadingKeys || !templateMeta.schema}>
          <InputLabel>Key</InputLabel>
          <Select
            value={templateMeta.key || ''}
            onChange={(e) => updateTemplateProperties({ key: e.target.value })}
            label="Key"
          >
            <MenuItem value="">
              <em>None</em>
            </MenuItem>
            {keys.map((key) => (
              <MenuItem key={key} value={key}>
                {key}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Format Selection */}
        <FormControl size="small" fullWidth disabled={updatingTemplate}>
          <InputLabel>Output Format</InputLabel>
          <Select
            value={templateMeta.format || 'PNG'}
            onChange={(e) => updateTemplateProperties({ format: e.target.value })}
            label="Output Format"
          >
            {formats.map((format) => (
              <MenuItem key={format.value} value={format.value}>
                {format.label}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Used Variables Display */}
        {availableVariables.length > 0 && (
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600 }}>
              Available Variables ({availableVariables.length})
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
              {availableVariables.slice(0, 10).map((variable, index) => (
                <Chip
                  key={index}
                  label={typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable))}
                  size="small"
                  variant="outlined"
                  sx={{
                    backgroundColor: 'rgba(79, 38, 131, 0.1)',
                    borderColor: '#4f2683',
                    color: '#4f2683',
                    fontSize: '0.75rem'
                  }}
                />
              ))}
              {availableVariables.length > 10 && (
                <Chip
                  label={`+${availableVariables.length - 10} more`}
                  size="small"
                  variant="outlined"
                  sx={{
                    backgroundColor: 'rgba(107, 114, 128, 0.1)',
                    borderColor: '#6b7280',
                    color: '#6b7280',
                    fontSize: '0.75rem'
                  }}
                />
              )}
            </Box>
          </Box>
        )}

        {/* Template Info */}
        <Box sx={{ mt: 1, p: 1.5, bgcolor: 'grey.50', borderRadius: 1 }}>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mb: 0.5 }}>
            Template ID: {currentTemplate.id || 'Not saved'}
          </Typography>
          <Typography variant="caption" color="text.secondary" sx={{ display: 'block' }}>
            Last Modified: {currentTemplate.lastModified ? new Date(currentTemplate.lastModified).toLocaleString() : 'Never'}
          </Typography>
        </Box>
      </Box>
    </Box>
  );

  // Handle preview mode
  if (previewMode) {
    return (
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          p: 3,
          textAlign: 'center',
        }}
      >
        <Box sx={{ mb: 3 }}>
          <PreviewIcon sx={{ fontSize: 48, color: 'primary.main', mb: 2 }} />
          <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600, mb: 1 }}>
            Preview Mode Enabled
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
            You are currently viewing the design in preview mode. Element editing is disabled.
          </Typography>
        </Box>

        <Button
          variant="contained"
          onClick={() => dispatch(togglePreviewMode())}
          startIcon={<SettingsIcon />}
          sx={{
            bgcolor: 'primary.main',
            '&:hover': {
              bgcolor: 'primary.dark',
            }
          }}
        >
          Exit Preview & View Properties
        </Button>

        <Typography variant="caption" color="text.secondary" sx={{ mt: 2 }}>
          Click to return to design mode and access element properties
        </Typography>
      </Box>
    );
  }

  const handlePropertyChange = (property, value) => {
    selectedElementIds.forEach(id => {
      dispatch(updateElement({
        id,
        properties: { [property]: value }
      }));
    });
  };

  const handleDelete = () => {
    selectedElementIds.forEach(id => {
      dispatch(deleteElement(id));
    });
  };

  const handleDuplicate = () => {
    if (selectedElement) {
      dispatch(duplicateElement(selectedElement.id));
    }
  };

  const renderCanvasProperties = () => (
    <Box sx={{ p: 2 }}>
      <Typography variant="subtitle2" sx={{ mb: 2, fontWeight: 600 }}>
        Canvas Settings
      </Typography>
      <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
          <Box sx={{ display: 'flex', gap: 1 }}>
            <TextField
              label={`Width (${canvasConfig.unit === UNITS.INCHES ? '"' : 'mm'})`}
              type="number"
              value={fromPixels(canvasConfig.width, canvasConfig.unit, canvasConfig.dpi).toFixed(2)}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                const minValue = canvasConfig.unit === UNITS.INCHES ? 0.5 : 12.7; // 0.5" or 12.7mm minimum

                if (value > 0 && value >= minValue) {
                  const pixels = toPixels(value, canvasConfig.unit, canvasConfig.dpi);
                  dispatch(updateCanvasConfig({ width: pixels }));
                }
              }}
              slotProps={{
                input: {
                  min: canvasConfig.unit === UNITS.INCHES ? 0.5 : 12.7,
                  step: canvasConfig.unit === UNITS.INCHES ? 0.1 : 1,
                }
              }}
              size="small"
              sx={{ flex: 1 }}
              helperText={`Min: ${canvasConfig.unit === UNITS.INCHES ? '0.5"' : '12.7mm'}`}
            />
            <TextField
              label={`Height (${canvasConfig.unit === UNITS.INCHES ? '"' : 'mm'})`}
              type="number"
              value={fromPixels(canvasConfig.height, canvasConfig.unit, canvasConfig.dpi).toFixed(2)}
              onChange={(e) => {
                const value = parseFloat(e.target.value);
                const minValue = canvasConfig.unit === UNITS.INCHES ? 0.5 : 12.7; // 0.5" or 12.7mm minimum

                if (value > 0 && value >= minValue) {
                  const pixels = toPixels(value, canvasConfig.unit, canvasConfig.dpi);
                  dispatch(updateCanvasConfig({ height: pixels }));
                }
              }}
              slotProps={{
                input: {
                  min: canvasConfig.unit === UNITS.INCHES ? 0.5 : 12.7,
                  step: canvasConfig.unit === UNITS.INCHES ? 0.1 : 1,
                }
              }}
              size="small"
              sx={{ flex: 1 }}
              helperText={`Min: ${canvasConfig.unit === UNITS.INCHES ? '0.5"' : '12.7mm'}`}
            />
          </Box>

          <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
            <FormControl size="small" sx={{ flex: 1 }}>
              <InputLabel>Units</InputLabel>
              <Select
                value={canvasConfig.unit}
                onChange={(e) => dispatch(setCanvasUnit(e.target.value))}
                label="Units"
              >
                <MenuItem value={UNITS.INCHES}>Inches</MenuItem>
                <MenuItem value={UNITS.MILLIMETERS}>Millimeters</MenuItem>
              </Select>
            </FormControl>
            <FormControlLabel
              control={
                <Switch
                  checked={canvasConfig.showRulers}
                  onChange={() => dispatch(toggleRulers())}
                />
              }
              label="Rulers"
            />
          </Box>

          <TextField
            label="Background Color"
            type="color"
            value={canvasConfig.background}
            onChange={(e) => dispatch(updateCanvasConfig({ background: e.target.value }))}
            size="small"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.showGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ showGrid: e.target.checked }))}
              />
            }
            label="Show Grid"
          />

          <FormControlLabel
            control={
              <Switch
                checked={canvasConfig.snapToGrid}
                onChange={(e) => dispatch(updateCanvasConfig({ snapToGrid: e.target.checked }))}
              />
            }
            label="Snap to Grid"
          />

          <Box>
            <Typography variant="caption" gutterBottom>
              Grid Size: {formatWithUnit(fromPixels(getGridSizeInPixels(canvasConfig.unit, canvasConfig.dpi), canvasConfig.unit, canvasConfig.dpi), canvasConfig.unit)}
            </Typography>
            <Typography variant="caption" color="text.secondary">
              Grid spacing is automatically set based on unit system
            </Typography>
          </Box>

          {/* Predefined Card Sizes */}
          <Box>
            <Typography variant="caption" gutterBottom>
              Standard Card Sizes
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR80 (Standard)' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR80 (Standard)' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR80 (Standard)',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CardIcon fontSize="small" />
                <Typography variant="caption">CR80</Typography>
                <Typography variant="caption" color="text.secondary">
                  3.375" × 2.125"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR79 (Government)' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR79 (Government)' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR79 (Government)',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <BadgeIcon fontSize="small" />
                <Typography variant="caption">CR79</Typography>
                <Typography variant="caption" color="text.secondary">
                  3.303" × 2.051"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'Custom' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'Custom' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'Custom',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CustomIcon fontSize="small" />
                <Typography variant="caption">Custom</Typography>
                <Typography variant="caption" color="text.secondary">
                  4" × 3"
                </Typography>
              </Box>
            </Box>

            {/* Vertical Card Sizes */}
            <Typography variant="caption" gutterBottom sx={{ mt: 2, display: 'block' }}>
              Vertical Card Sizes
            </Typography>
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR80 Vertical' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR80 Vertical' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR80 Vertical',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CardIcon fontSize="small" sx={{ transform: 'rotate(90deg)' }} />
                <Typography variant="caption">CR80 V</Typography>
                <Typography variant="caption" color="text.secondary">
                  2.125" × 3.375"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'CR79 Vertical' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'CR79 Vertical' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'CR79 Vertical',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <BadgeIcon fontSize="small" sx={{ transform: 'rotate(90deg)' }} />
                <Typography variant="caption">CR79 V</Typography>
                <Typography variant="caption" color="text.secondary">
                  2.051" × 3.303"
                </Typography>
              </Box>

              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  p: 1,
                  border: 1,
                  borderColor: canvasConfig.cardType === 'Custom Vertical' ? 'primary.main' : 'divider',
                  borderRadius: 1,
                  cursor: 'pointer',
                  bgcolor: canvasConfig.cardType === 'Custom Vertical' ? 'primary.light' : 'transparent',
                  '&:hover': { bgcolor: 'action.hover' }
                }}
                onClick={() => dispatch(setCardType({
                  cardType: 'Custom Vertical',
                  unit: canvasConfig.unit,
                  dpi: canvasConfig.dpi
                }))}
              >
                <CustomIcon fontSize="small" sx={{ transform: 'rotate(90deg)' }} />
                <Typography variant="caption">Custom V</Typography>
                <Typography variant="caption" color="text.secondary">
                  3" × 4"
                </Typography>
              </Box>
            </Box>
          </Box>
        </Box>
      </Box>
  );

  // Show canvas properties when no elements are selected
  if (selectedElementIds.length === 0) {
    return (
      <Box
        sx={{
          height: '100%',
          overflow: 'auto',
          '& .MuiOutlinedInput-root': {
            marginTop: 0,
          },
          '& .MuiTextField-root': {
            marginTop: 0,
          }
        }}
      >
        <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
          <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
            Canvas Properties
          </Typography>
        </Box>
        <Box sx={{ overflow: 'auto' }}>
          {renderTemplateProperties()}
          <Box sx={{ borderTop: 1, borderColor: 'divider', mt: 2 }}>
            {renderCanvasProperties()}
          </Box>
        </Box>
      </Box>
    );
  }

  const renderTextProperties = () => {
    if (!selectedElement || selectedElement.type !== 'text') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Text Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextEditorWithVariables
              value={selectedElement.text || ''}
              onChange={(value) => handlePropertyChange('text', value)}
              label="Text Content"
              helperText="Type {{ to insert variables"
              availableVariables={availableVariables.map(v => typeof v === 'string' ? v : (v.name || v.id || String(v)))}
              multiline
              rows={2}
              size="small"
            />
            
            <FormControl size="small">
              <InputLabel>Font Family</InputLabel>
              <Select
                value={selectedElement.fontFamily || 'Arial'}
                onChange={(e) => handlePropertyChange('fontFamily', e.target.value)}
                label="Font Family"
              >
                <MenuItem value="Arial">Arial</MenuItem>
                <MenuItem value="Helvetica">Helvetica</MenuItem>
                <MenuItem value="Times New Roman">Times New Roman</MenuItem>
                <MenuItem value="Courier New">Courier New</MenuItem>
                <MenuItem value="Georgia">Georgia</MenuItem>
                <MenuItem value="Verdana">Verdana</MenuItem>
              </Select>
            </FormControl>

            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Typography variant="caption" gutterBottom sx={{ display: 'block', whiteSpace: 'nowrap' }}>
                Font Size: {selectedElement.fontSize || 16}px
              </Typography>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.fontSize || 16}
                  onChange={(_, value) => handlePropertyChange('fontSize', value)}
                  min={8}
                  max={72}
                  size="small"
                />
              </Box>
            </Box>

            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Typography variant="caption" gutterBottom sx={{ display: 'block', whiteSpace: 'nowrap' }}>
                Line Height: {(selectedElement.lineHeight || 1.2).toFixed(1)}
              </Typography>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.lineHeight || 1.2}
                  onChange={(_, value) => handlePropertyChange('lineHeight', value)}
                  min={0.8}
                  max={3.0}
                  step={0.1}
                  size="small"
                  marks={[
                    { value: 1.0, label: '1.0' },
                    { value: 1.2, label: '1.2' },
                    { value: 1.5, label: '1.5' },
                    { value: 2.0, label: '2.0' }
                  ]}
                />
              </Box>
            </Box>

            <TextField
              label="Text Color"
              type="color"
              value={selectedElement.color || '#000000'}
              onChange={(e) => handlePropertyChange('color', e.target.value)}
              size="small"
            />

            {/* Text Alignment */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Text Alignment
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'left')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'left' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'left' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignLeftIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'center')}
                  sx={{
                    bgcolor: (selectedElement.textAlign || 'center') === 'center' ? 'primary.light' : 'transparent',
                    color: (selectedElement.textAlign || 'center') === 'center' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignCenterIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'right')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'right' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'right' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignRightIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('textAlign', 'justify')}
                  sx={{
                    bgcolor: selectedElement.textAlign === 'justify' ? 'primary.light' : 'transparent',
                    color: selectedElement.textAlign === 'justify' ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <AlignJustifyIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            {/* Text Formatting */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Text Formatting
              </Typography>
              <Box sx={{ display: 'flex', gap: 0.5 }}>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentStyle = selectedElement.fontStyle || 'normal';
                    const newStyle = currentStyle.includes('bold') ? 'normal' : 'bold';
                    handlePropertyChange('fontStyle', newStyle);
                  }}
                  sx={{
                    bgcolor: (selectedElement.fontStyle || '').includes('bold') ? 'primary.light' : 'transparent',
                    color: (selectedElement.fontStyle || '').includes('bold') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <BoldIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentStyle = selectedElement.fontStyle || 'normal';
                    const newStyle = currentStyle.includes('italic') ? 'normal' : 'italic';
                    handlePropertyChange('fontStyle', newStyle);
                  }}
                  sx={{
                    bgcolor: (selectedElement.fontStyle || '').includes('italic') ? 'primary.light' : 'transparent',
                    color: (selectedElement.fontStyle || '').includes('italic') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <ItalicIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentDecoration = selectedElement.textDecoration || '';
                    const newDecoration = currentDecoration.includes('underline') ? '' : 'underline';
                    handlePropertyChange('textDecoration', newDecoration);
                  }}
                  sx={{
                    bgcolor: (selectedElement.textDecoration || '').includes('underline') ? 'primary.light' : 'transparent',
                    color: (selectedElement.textDecoration || '').includes('underline') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <UnderlineIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => {
                    const currentDecoration = selectedElement.textDecoration || '';
                    const newDecoration = currentDecoration.includes('line-through') ? '' : 'line-through';
                    handlePropertyChange('textDecoration', newDecoration);
                  }}
                  sx={{
                    bgcolor: (selectedElement.textDecoration || '').includes('line-through') ? 'primary.light' : 'transparent',
                    color: (selectedElement.textDecoration || '').includes('line-through') ? 'primary.contrastText' : 'inherit'
                  }}
                >
                  <StrikethroughIcon fontSize="small" />
                </IconButton>
              </Box>
            </Box>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.autoResize !== false}
                  onChange={(e) => handlePropertyChange('autoResize', e.target.checked)}
                />
              }
              label="Auto-resize Font"
            />
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderImageProperties = () => {
    if (!selectedElement || selectedElement.type !== 'image') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Image Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Image URL"
              value={selectedElement.src || selectedElement.dynamicSrc || ''}
              onChange={(e) => handlePropertyChange(selectedElement.isDynamic ? 'dynamicSrc' : 'src', e.target.value)}
              size="small"
              helperText={
                selectedElement.hasFile && imageManager.hasPendingUpload(selectedElement.id)
                  ? "📤 Image ready for upload (will be uploaded when template is saved)"
                  : selectedElement.isDynamic
                    ? "Use {{variable}} for dynamic images"
                    : "Enter image URL or upload file"
              }
              slotProps={{
                input: {
                  style: {
                    backgroundColor: selectedElement.hasFile && imageManager.hasPendingUpload(selectedElement.id)
                      ? 'rgba(255, 193, 7, 0.1)'
                      : undefined
                  }
                }
              }}
            />

            {selectedElement.isDynamic && availableVariables.length > 0 && (
              <Box>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Available Variables:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {availableVariables.map((variable, index) => (
                    <Chip
                      key={index}
                      label={typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable))}
                      size="small"
                      onClick={() => {
                        const currentSrc = selectedElement.dynamicSrc || '';
                        const variableName = typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable));
                        const newSrc = currentSrc + `{{${variableName}}}`;
                        handlePropertyChange('dynamicSrc', newSrc);
                      }}
                      sx={{
                        fontSize: '0.7rem',
                        height: 24,
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'primary.light',
                          color: 'white'
                        }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            
            <Button
              variant="outlined"
              size="small"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = (event) => {
                  const file = event.target.files[0];
                  if (file) {
                    try {
                      // Add image to image manager and get temporary URL
                      const tempUrl = imageManager.addImage(
                        selectedElement.id,
                        file,
                        selectedElement.imageType || 'static'
                      );

                      // Update element with temporary URL for immediate display
                      handlePropertyChange('src', tempUrl);
                      handlePropertyChange('hasFile', true);
                    } catch (error) {
                      console.error('Error handling image file:', error);
                    }
                  }
                };
                input.click();
              }}
              disabled={selectedElement.isDynamic}
            >
              Upload Image
            </Button>

            <FormControlLabel
              control={
                <Switch
                  checked={selectedElement.maintainAspectRatio !== false}
                  onChange={(e) => handlePropertyChange('maintainAspectRatio', e.target.checked)}
                />
              }
              label="Maintain Aspect Ratio"
            />

            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Typography variant="caption" gutterBottom sx={{ display: 'block', whiteSpace: 'nowrap' }}>
                Border Radius: {selectedElement.borderRadius || 0}px
              </Typography>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.borderRadius || 0}
                  onChange={(_, value) => handlePropertyChange('borderRadius', value)}
                  min={0}
                  max={Math.min(selectedElement.width || 100, selectedElement.height || 100) / 2}
                  step={1}
                  size="small"
                  marks={[
                    { value: 0, label: '0' },
                    { value: Math.min(selectedElement.width || 100, selectedElement.height || 100) / 4, label: 'Round' },
                    { value: Math.min(selectedElement.width || 100, selectedElement.height || 100) / 2, label: 'Circle' }
                  ]}
                />
              </Box>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderQRCodeProperties = () => {
    if (!selectedElement || selectedElement.type !== 'qrcode') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">QR Code Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="QR Code Data"
              value={selectedElement.data || ''}
              onChange={(e) => handlePropertyChange('data', e.target.value)}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />

            {availableVariables.length > 0 && (
              <Box>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Available Variables:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {availableVariables.map((variable, index) => (
                    <Chip
                      key={index}
                      label={typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable))}
                      size="small"
                      onClick={() => {
                        const currentData = selectedElement.data || '';
                        const variableName = typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable));
                        const newData = currentData + `{{${variableName}}}`;
                        handlePropertyChange('data', newData);
                      }}
                      sx={{
                        fontSize: '0.7rem',
                        height: 24,
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'primary.light',
                          color: 'white'
                        }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}

            <FormControl size="small">
              <InputLabel>Error Correction</InputLabel>
              <Select
                value={selectedElement.errorCorrectionLevel || 'M'}
                onChange={(e) => handlePropertyChange('errorCorrectionLevel', e.target.value)}
                label="Error Correction"
              >
                <MenuItem value="L">Low (7%)</MenuItem>
                <MenuItem value="M">Medium (15%)</MenuItem>
                <MenuItem value="Q">Quartile (25%)</MenuItem>
                <MenuItem value="H">High (30%)</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderBarcodeProperties = () => {
    if (!selectedElement || selectedElement.type !== 'barcode') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Barcode Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <TextField
              label="Barcode Data"
              value={selectedElement.data || ''}
              onChange={(e) => handlePropertyChange('data', e.target.value)}
              size="small"
              helperText="Use {{variable}} for dynamic content"
            />

            {availableVariables.length > 0 && (
              <Box>
                <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
                  Available Variables:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                  {availableVariables.map((variable, index) => (
                    <Chip
                      key={index}
                      label={typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable))}
                      size="small"
                      onClick={() => {
                        const currentData = selectedElement.data || '';
                        const variableName = typeof variable === 'string' ? variable : (variable.name || variable.id || String(variable));
                        const newData = currentData + `{{${variableName}}}`;
                        handlePropertyChange('data', newData);
                      }}
                      sx={{
                        fontSize: '0.7rem',
                        height: 24,
                        cursor: 'pointer',
                        '&:hover': {
                          backgroundColor: 'primary.light',
                          color: 'white'
                        }
                      }}
                    />
                  ))}
                </Box>
              </Box>
            )}

            <FormControl size="small">
              <InputLabel>Barcode Format</InputLabel>
              <Select
                value={selectedElement.format || 'CODE128'}
                onChange={(e) => handlePropertyChange('format', e.target.value)}
                label="Barcode Format"
              >
                <MenuItem value="CODE128">CODE128</MenuItem>
                <MenuItem value="CODE39">CODE39</MenuItem>
                <MenuItem value="EAN13">EAN13</MenuItem>
                <MenuItem value="EAN8">EAN8</MenuItem>
                <MenuItem value="UPC">UPC</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderShapeProperties = () => {
    if (!selectedElement || selectedElement.type !== 'shape') return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Shape Properties</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Fill Color */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Fill Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <input
                  type="color"
                  value={selectedElement.fill || '#4f2683'}
                  onChange={(e) => handlePropertyChange('fill', e.target.value)}
                  style={{
                    width: 40,
                    height: 32,
                    border: '1px solid #ccc',
                    borderRadius: 4,
                    cursor: 'pointer'
                  }}
                />
                <TextField
                  value={selectedElement.fill || '#4f2683'}
                  onChange={(e) => handlePropertyChange('fill', e.target.value)}
                  size="small"
                  sx={{ flex: 1 }}
                />
              </Box>
            </Box>

            {/* Stroke Color */}
            <Box>
              <Typography variant="caption" gutterBottom>
                Border Color
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, alignItems: 'center' }}>
                <input
                  type="color"
                  value={selectedElement.stroke || '#4f2683'}
                  onChange={(e) => handlePropertyChange('stroke', e.target.value)}
                  style={{
                    width: 40,
                    height: 32,
                    border: '1px solid #ccc',
                    borderRadius: 4,
                    cursor: 'pointer'
                  }}
                />
                <TextField
                  value={selectedElement.stroke || '#4f2683'}
                  onChange={(e) => handlePropertyChange('stroke', e.target.value)}
                  size="small"
                  sx={{ flex: 1 }}
                />
              </Box>
            </Box>

            {/* Stroke Width */}
            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Typography variant="caption" gutterBottom sx={{ display: 'block', whiteSpace: 'nowrap' }}>
                Border Width: {selectedElement.strokeWidth || 1}px
              </Typography>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.strokeWidth || 1}
                  onChange={(_, value) => handlePropertyChange('strokeWidth', value)}
                  min={0}
                  max={10}
                  step={1}
                  size="small"
                  marks={[
                    { value: 0, label: '0' },
                    { value: 5, label: '5' },
                    { value: 10, label: '10' }
                  ]}
                />
              </Box>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  const renderPositionProperties = () => {
    if (!selectedElement) return null;

    return (
      <Accordion defaultExpanded>
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <Typography variant="subtitle2">Position & Size</Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="X"
                type="number"
                value={Math.round(selectedElement.x || 0)}
                onChange={(e) => handlePropertyChange('x', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Y"
                type="number"
                value={Math.round(selectedElement.y || 0)}
                onChange={(e) => handlePropertyChange('y', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <TextField
                label="Width"
                type="number"
                value={Math.round(selectedElement.width || 0)}
                onChange={(e) => handlePropertyChange('width', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
              <TextField
                label="Height"
                type="number"
                value={Math.round(selectedElement.height || 0)}
                onChange={(e) => handlePropertyChange('height', parseFloat(e.target.value))}
                size="small"
                sx={{ flex: 1 }}
              />
            </Box>

            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1, minWidth: 0 }}>
                <RotateIcon sx={{ fontSize: 16, flexShrink: 0 }} />
                <Typography variant="caption" sx={{ flexShrink: 0, whiteSpace: 'nowrap' }}>
                  Rotation: {Math.round(selectedElement.rotation || 0)}°
                </Typography>
                <IconButton
                  size="small"
                  onClick={() => handlePropertyChange('rotation', (selectedElement.rotation || 0) + 90)}
                  title="Rotate 90°"
                  sx={{ flexShrink: 0 }}
                >
                  <RotateIcon sx={{ fontSize: 16 }} />
                </IconButton>
              </Box>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.rotation || 0}
                  onChange={(_, value) => handlePropertyChange('rotation', value)}
                  min={-180}
                  max={180}
                  size="small"
                />
              </Box>
            </Box>

            <Box sx={{ width: '100%', overflow: 'hidden' }}>
              <Typography variant="caption" gutterBottom sx={{ display: 'block', whiteSpace: 'nowrap' }}>
                Opacity: {Math.round((selectedElement.opacity || 1) * 100)}%
              </Typography>
              <Box sx={{ width: '100%', px: 1 }}>
                <Slider
                  value={selectedElement.opacity || 1}
                  onChange={(_, value) => handlePropertyChange('opacity', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  size="small"
                />
              </Box>
            </Box>
          </Box>
        </AccordionDetails>
      </Accordion>
    );
  };

  return (
    <Box
      sx={{
        height: '100%',
        overflow: 'auto',
        overflowX: 'hidden', // Prevent horizontal scrolling
        '& .MuiOutlinedInput-root': {
          marginTop: 0,
        },
        '& .MuiTextField-root': {
          marginTop: 0,
        },
        '& .MuiSlider-root': {
          maxWidth: '100%', // Ensure sliders don't overflow
        }
      }}
    >
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Typography variant="h6" sx={{ fontSize: '1rem', fontWeight: 600 }}>
          Properties
        </Typography>
        {selectedElementIds.length > 1 && (
          <Typography variant="caption" color="text.secondary">
            {selectedElementIds.length} elements selected
          </Typography>
        )}
      </Box>

      {/* Element Actions */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <IconButton
            size="small"
            onClick={handleDuplicate}
            disabled={selectedElementIds.length !== 1}
            title="Duplicate"
            sx={{
              color: 'grey.600',
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'primary.light',
              },
              '&:disabled': {
                color: 'grey.400',
              }
            }}
          >
            <DuplicateIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={handleDelete}
            title="Delete"
            sx={{
              color: 'grey.600',
              '&:hover': {
                color: 'error.main',
                bgcolor: 'error.light',
              }
            }}
          >
            <DeleteIcon />
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('locked', !selectedElement?.locked)}
            title={selectedElement?.locked ? 'Unlock' : 'Lock'}
            sx={{
              color: selectedElement?.locked ? 'primary.main' : 'grey.600',
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'primary.light',
              }
            }}
          >
            {selectedElement?.locked ? <LockIcon /> : <UnlockIcon />}
          </IconButton>

          <IconButton
            size="small"
            onClick={() => handlePropertyChange('visible', !selectedElement?.visible)}
            title={selectedElement?.visible ? 'Hide' : 'Show'}
            sx={{
              color: 'grey.600',
              '&:hover': {
                color: 'primary.main',
                bgcolor: 'primary.light',
              }
            }}
          >
            {selectedElement?.visible !== false ? <VisibilityIcon /> : <VisibilityOffIcon />}
          </IconButton>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToFront(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Front
          </Button>
          <Button
            size="small"
            variant="outlined"
            onClick={() => selectedElement && dispatch(moveElementToBack(selectedElement.id))}
            disabled={!selectedElement}
          >
            To Back
          </Button>
        </Box>
      </Box>

      {/* Dynamic Properties */}
      <Box>
        {renderPositionProperties()}
        {renderTextProperties()}
        {renderImageProperties()}
        {renderShapeProperties()}
        {renderQRCodeProperties()}
        {renderBarcodeProperties()}
        {/* Only show canvas properties if no elements are selected */}
        {selectedElementIds.length === 0 && renderCanvasProperties()}
      </Box>
    </Box>
  );
};

export default PropertyPanel;
