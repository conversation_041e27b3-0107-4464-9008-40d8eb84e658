import api from "./";

/**
 * Retrieves a paginated list of facilities.
 *
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated facility details.
 */
export const getFacilities = async (params = {}) => {
  const response = await api.get("facility", { params });
  return response.data;
};

/**
 * Retrieves facility details by ID.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @returns {Promise<any>} A promise that resolves to the facility details.
 */
export const getFacilityById = async (facilityId) => {
  const response = await api.get(`facility/${facilityId}`);
  return response.data;
};

/**
 * Creates a new facility.
 *
 * @param {object} facilityData - The data for the new facility including name, facility_code, facility_type, time_zone, phone, email, and address.
 * @returns {Promise<any>} A promise that resolves to the response data containing the newly created facility.
 */
export const createFacility = async (facilityData) => {
  const response = await api.post("facility", facilityData);
  return response.data;
};

/**
 * Updates facility details.
 *
 * @param {string} facilityId - The unique identifier of the facility to update.
 * @param {object} facilityData - The data to update. This may include properties such as name, facility_code, facility_type, time_zone, phone, email, status, etc.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated facility.
 */
export const updateFacility = async (facilityId, facilityData) => {
  const response = await api.patch(`facility/${facilityId}`, facilityData);
  return response.data;
};

/**
 * Changes the status of a facility.
 *
 * @param {string} facilityId - The unique identifier of the facility whose status is to be changed.
 * @param {object} statusData - An object containing the new status (e.g., { status: "Inactive" }).
 * @returns {Promise<any>} A promise that resolves to the response data confirming the status update.
 */
export const updateFacilityStatus = async (facilityId, statusData) => {
  const response = await api.patch(`facility/${facilityId}/status`, statusData);
  return response.data;
};

/**
 * Updates the address of a facility.
 *
 * @param {string} facilityId - The unique identifier of the facility whose address is to be updated.
 * @param {object} addressData - The new address data including address_line_1, country, state_province, postal_code, etc.
 * @returns {Promise<any>} A promise that resolves to the response data confirming the address update.
 */
export const updateFacilityAddress = async (facilityId, addressData) => {
  const response = await api.patch(
    `facility/${facilityId}/address`,
    addressData
  );
  return response.data;
};

// ######################### building ################

/**
 * Retrieves a paginated list of buildings for a specific facility.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated building details.
 */
export const getBuildingsByFacility = async (facilityId, params = {}) => {
  const response = await api.get(`facility/buildings/${facilityId}`, { params });
  return response.data;
};

/**
 * Retrieves building details by facility ID and building ID.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} buildingId - The unique identifier of the building.
 * @returns {Promise<any>} A promise that resolves to the building details.
 */
export const getBuildingById = async (facilityId, buildingId) => {
  const response = await api.get(`facility/buildings/${facilityId}/${buildingId}`);
  return response.data;
};

/**
 * Creates a new building for a facility.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {object} buildingData - The data for the new building including name, address, year_constructed, building_code, type, occupancy_type, phone, email, geo_location_code, other_code, building_url, connected_applications, and notes.
 * @returns {Promise<any>} A promise that resolves to the response data containing the newly created building.
 */
export const createBuilding = async (facilityId, buildingData) => {
  const response = await api.post(`facility/buildings/${facilityId}`, buildingData);
  return response.data;
};

/**
 * Updates building details.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} buildingId - The unique identifier of the building to update.
 * @param {object} buildingData - The data to update. This may include properties such as name, address, year_constructed, building_code, status, type, occupancy_type, phone, email, geo_location_code, other_code, building_url, connected_applications, and notes.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated building.
 */
export const updateBuilding = async (facilityId, buildingId, buildingData) => {
  const response = await api.patch(
    `facility/buildings/${facilityId}/${buildingId}`,
    buildingData
  );
  return response.data;
};

/**
 * Changes the status of a building.
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} buildingId - The unique identifier of the building whose status is to be changed.
 * @param {object} statusData - An object containing the new status (e.g., { status: "Inactive" }).
 * @returns {Promise<any>} A promise that resolves to the response data confirming the status update.
 */
export const updateBuildingStatus = async (
  facilityId,
  buildingId,
  statusData
) => {
  const response = await api.patch(
    `facility/buildings/${facilityId}/${buildingId}/status`,
    statusData
  );
  return response.data;
};

// #####floor#####

/**
 * Retrieves a paginated list of floors for a specific building.
 *
 * @param {string} facilityId - The unique identifier of the building.
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated floor details.
 */
export const getFloorsByBuilding = async (facilityId, params = {}) => {
  const response = await api.get(`facility/floors/${facilityId}`, { params });
  return response.data;
};

/**
 * Retrieves floor details by building ID and floor ID.
 *
 * @param {string} buildingId - The unique identifier of the building.
 * @param {string} floorId - The unique identifier of the floor.
 * @returns {Promise<any>} A promise that resolves to the floor details.
 */
export const getFloorById = async (facilityId, floorId) => {
  const response = await api.get(`/facility/floors/${facilityId}/${floorId}`);
  return response.data;
};

/**
 * Creates a new floor in a building.
 *
 * @param {string} facilityId - The unique identifier of the building.
 * @param {object} floorData - The data for the new floor including floor_number, total_square_footage, max_occupancy, and occupancy_type.
 * @returns {Promise<any>} A promise that resolves to the response data containing the newly created floor.
 */
export const createFloor = async (facilityId, floorData) => {
  const response = await api.post(`facility/floors/${facilityId}`, floorData);
  return response.data;
};

/**
 * Updates floor details.
 *
 * @param {string} buildingId - The unique identifier of the building.
 * @param {string} floorId - The unique identifier of the floor to update.
 * @param {object} floorData - The data to update. This may include properties such as floor_number, total_square_footage, max_occupancy, and occupancy_type.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated floor.
 */
export const updateFloor = async (facilityId, floorId, floorData) => {
  const response = await api.patch(
    `/facility/floors/${facilityId}/${floorId}`,
    floorData
  );
  return response.data;
};

/**
 * Changes the status of a floor.
 ** @param {string} facilityId - The unique identifier of the facility.
 * @param {string} buildingId - The unique identifier of the building whose status is to be changed.
 * @param {object} statusData - An object containing the new status.
 * @returns {Promise<any>} A promise that resolves to the response data confirming the status update.
 */
// export const updateBuildingStatus = async (facilityId, buildingId, statusData) => {
//   const response = await api.patch(`buildings/${facilityId}/${buildingId}/status`, statusData);
//   return response.data;
// };

// #####room#####

/**
 * Retrieves a paginated list of rooms for a specific floor.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @param {object} params - Query parameters including page and limit.
 * @returns {Promise<any>} A promise that resolves to the response data containing paginated room details.
 */
export const getRoomsByRooms = async (facilityId, params = {}) => {
  const response = await api.get(`facility/rooms/${facilityId}`, { params });
  return response.data;
};

/**
 * Retrieves room details by floor ID and room ID.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @param {string} roomId - The unique identifier of the room.
 * @returns {Promise<any>} A promise that resolves to the room details.
 */
export const getRoomById = async (facilityId, roomId) => {
  const response = await api.get(`facility/rooms/${facilityId}`);
  return response.data;
};

/**
 * Creates a new room in a floor.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @param {object} roomData - The data for the new room including room_number, max_occupancy, area, primary_contact_name, primary_contact_number, primary_contact_email, and status.
 * @returns {Promise<any>} A promise that resolves to the response data containing the newly created room.
 */
export const createRoom = async (facilityId, roomData) => {
  const response = await api.post(`facility/rooms/${facilityId}`, roomData);
  return response.data;
};

/**
 * Updates room details.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @param {string} roomId - The unique identifier of the room to update.
 * @param {object} roomData - The data to update. This may include properties such as room_number, max_occupancy, area, primary_contact_name, primary_contact_number, primary_contact_email, and status.
 * @returns {Promise<any>} A promise that resolves to the response data containing the updated room.
 */// Example: यदि floorId URL का हिस्सा होना चाहिए
 export const updateRoom = async (facilityId, roomId, roomData) => {
  const response = await api.patch(`facility/rooms/${facilityId}/${roomId}`, roomData);
  return response.data;
};


/**
 * Changes the status of a room.
 *
 * @param {string} floorId - The unique identifier of the floor.
 * @param {string} roomId - The unique identifier of the room whose status is to be changed.
 * @param {object} statusData - An object containing the new status (e.g., { status: "Inactive" }).
 * @returns {Promise<any>} A promise that resolves to the response data confirming the status update.
 */
export const updateRoomStatus = async (facilityId, roomId, statusData) => {
  const response = await api.patch(
    `facility/rooms/${facilityId}/${roomId}/status`,
    statusData
  );
  return response.data;
};

  //  ### ///
  

  // /**
  //  * Retrieves a paginated list of access levels for a facility.
  //  *
  //  * @param {string} facilityId - The unique identifier of the facility.
  //  * @param {object} params - Query parameters including page and limit.
  //  * @returns {Promise<any>} A promise that resolves to the response data containing paginated access level details.
  //  */
  // export const getAccessLevels = async (facilityId, params = {}) => {
  //   // Matches GET /access-levels/{facilityId}
  //   const response = await api.get(`/access-levels/${facilityId}`, { params });
  //   return response.data;
  // };
  
  // /**
  //  * Retrieves access level details by facility and access level ID.
  //  *
  //  * @param {string} facilityId - The unique identifier of the facility.
  //  * @param {string} facilityAccessLevelId - The unique identifier of the facility access level.
  //  * @returns {Promise<any>} A promise that resolves to the access level details.
  //  */
  // export const getAccessLevelById = async (facilityId, facilityAccessLevelId) => {
  //   // Matches GET /access-levels/{facilityId}/{facilityAccessLevelId}
  //   const response = await api.get(
  //     `/access-levels/${facilityId}/${facilityAccessLevelId}`
  //   );
  //   return response.data;
  // };
  
  // /**
  //  * Creates a new access level for a facility.
  //  *
  //  * @param {string} facilityId - The unique identifier of the facility.
  //  * @param {object} accessLevelData - The data for the new access level, e.g.:
  //  *        { access_level_id, building_id, floor_id, room_id,
  //  *          entry_restrictions, access_protocol, ... }
  //  * @returns {Promise<any>} A promise that resolves to the newly created access level.
  //  */
  // export const createAccessLevel = async (facilityId, accessLevelData) => {
  //   // Matches POST /access-levels/{facilityId}
  //   const response = await api.post(`/access-levels/${facilityId}`, accessLevelData);
  //   return response.data;
  // };
  
  // /**
  //  * Updates access level details for a facility.
  //  *
  //  * @param {string} facilityId - The unique identifier of the facility.
  //  * @param {string} facilityAccessLevelId - The unique identifier of the access level to update.
  //  * @param {object} accessLevelData - The data to update.
  //  * @returns {Promise<any>} A promise that resolves to the updated access level.
  //  */
  // export const updateAccessLevel = async (
  //   facilityId,
  //   facilityAccessLevelId,
  //   accessLevelData
  // ) => {
  //   // Matches PATCH /access-levels/{facilityId}/{facilityAccessLevelId}
  //   const response = await api.patch(
  //     `/access-levels/${facilityId}/${facilityAccessLevelId}`,
  //     accessLevelData
  //   );
  //   return response.data;
  // };
  
  // /**
  //  * Changes the status of an access level. (If your Swagger has a separate endpoint for status)
  //  * 
  //  * If your Swagger includes something like:
  //  *    PATCH /access-levels/{facilityId}/{facilityAccessLevelId}/status
  //  * you can do:
  //  *    export const updateAccessLevelStatus = async (facilityId, facilityAccessLevelId, statusData) => {
  //  *      const response = await api.patch(
  //  *        `/access-levels/${facilityId}/${facilityAccessLevelId}/status`,
  //  *        statusData
  //  *      );
  //  *      return response.data;
  //  *    };
  //  * 
  //  * If there's no dedicated "status" route, remove or adjust accordingly.
  //  */
  // export const updateAccessLevelStatus = async (facilityId, facilityAccessLevelId, statusData) => {
  //   // Adjust this route if needed based on your actual backend endpoint.
  //   const response = await api.patch(
  //     `/access-levels/${facilityId}/${facilityAccessLevelId}/status`,
  //     statusData
  //   );
  //   return response.data;
  // };

// #####access-area#####


/**
 * Retrieves a paginated list of facility access levels.
 * 
 * Matches GET /access-levels/{facilityId}
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {object} params - Query parameters (e.g. { page, limit }).
 * @returns {Promise<any>} A promise that resolves to the response data containing the list of access levels.
 */
export const getAccessLevels = async (facilityId) => {
  // Ab facilityId ko URL path ka hissa banate hain
  const response = await api.get(`facility/access-levels/${facilityId}`);
  return response.data;
};


/**
 * Retrieves a single access level by its ID.
 * 
 * Matches GET /access-levels/{facilityId}/{facilityAccessLevelId}
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} facilityAccessLevelId - The unique identifier of the access level.
 * @returns {Promise<any>} A promise that resolves to the access level details.
 */
export const getAccessLevelById = async (facilityId, facilityAccessLevelId) => {
  const response = await api.get(`/facility/access-levels/${facilityId}/${facilityAccessLevelId}`);
  return response.data;
};

/**
 * Creates a new access level for a facility.
 * 
 * Matches POST /access-levels/{facilityId}
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {object} accessLevelData - The data for the new access level.
 * @returns {Promise<any>} A promise that resolves to the newly created access level.
 */
export const createAccessLevel = async (facilityId, accessLevelData) => {
  const response = await api.post(`/facility/access-levels/${facilityId}`, accessLevelData);
  return response.data;
};

/**
 * Updates an existing access level for a facility.
 * 
 * Matches PATCH /access-levels/{facilityId}/{facilityAccessLevelId}
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} facilityAccessLevelId - The unique identifier of the access level to update.
 * @param {object} accessLevelData - The data to update.
 * @returns {Promise<any>} A promise that resolves to the updated access level.
 */
export const updateAccessLevel = async (facilityId, facilityAccessLevelId, accessLevelData) => {
  const response = await api.patch(
    `/facility/access-levels/${facilityId}/${facilityAccessLevelId}`,
    accessLevelData
  );
  return response.data;
};


/**
 * Changes the status of an access level. (If you have a dedicated endpoint for status)
 * 
 * Matches PATCH /access-levels/{facilityId}/{facilityAccessLevelId}/status
 *
 * @param {string} facilityId - The unique identifier of the facility.
 * @param {string} facilityAccessLevelId - The unique identifier of the access level.
 * @param {object} statusData - An object containing the new status (e.g. { status: "Inactive" }).
 * @returns {Promise<any>} A promise that resolves to the updated access level.
 */
export const updateAccessLevelStatus = async (facilityId, facilityAccessLevelId, statusData) => {
  const response = await api.patch(
    `/facility/access-levels/${facilityId}/${facilityAccessLevelId}/status`,
    statusData
  );
  return response.data;
};

/**
 * Deletes a facility access level by its ID.
 * 
 * Matches DELETE /facility/access-levels/{facilityAccessLevelId}
 *
 * @param {string} facilityAccessLevelId - The unique identifier of the access level to delete.
 * @returns {Promise<any>} A promise that resolves to the response data after deletion.
 */
export const deleteAccessLevel = async (facilityAccessLevelId) => {
  const response = await api.delete(`/facility/access-levels/${facilityAccessLevelId}`);
  return response.data;
};
