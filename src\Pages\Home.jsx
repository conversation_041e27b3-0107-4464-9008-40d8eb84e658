import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; // Import useNavigate
import ImgRequestAccess from '../Images/CheckIn.svg';
import ImgRequestCard from '../Images/request-card.svg';
import ImgReportCard from '../Images/Last,Damaged.svg';
import ImgUpdatePhoto from '../Images/upload-photo.svg';
import ImgDelegate from '../Images/AddView Delegate.svg';
import ImgHostVisitor from '../Images/Host And Visitor.svg';
import Request from '../Images/empty.svg';
import window from '../Images/new-window.svg';
import AddAccessRequest from '../Components/MyApp/MyProfile/Access/AddAccessRequest'; // Import AddAccessRequest
import AddCardForm from '../Components/MyApp/MyProfile/Cards/AddCardForm'; // Import AddCardForm
import EditPhotoModal from '../Components/Global/ImageAndCamera/EditPhotoModal'; // Import EditPhotoModal

const Dashboard = () => {
  const navigate = useNavigate(); // Initialize navigate
  const [isModalOpen, setIsModalOpen] = useState(false); // State for AddAccessRequest modal
  const [isCardModalOpen, setIsCardModalOpen] = useState(false); // State for AddCardForm modal
  const [isPhotoModalOpen, setIsPhotoModalOpen] = useState(false); // State for EditPhotoModal

  // const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => setIsModalOpen(false);
  const handleOpenCardModal = () => setIsCardModalOpen(true);
  const handleCloseCardModal = () => setIsCardModalOpen(false);
  const handleOpenPhotoModal = () => navigate('/my-profile?tab=Identity&openPhotoModal=true'); // Navigate with query
  const handleClosePhotoModal = () => setIsPhotoModalOpen(false); // Close EditPhotoModal
  const handleNavigateToTab = (tab) => navigate(`/my-profile?tab=${tab}`); // Navigation handler

  const items = [
    { label: 'Request Access', img: ImgRequestAccess, onClick: () => handleNavigateToTab('Access') }, // Updated handler
    { label: 'Request Card', img: ImgRequestCard, onClick: () => handleNavigateToTab('Cards') }, // Updated handler
    { label: 'Lost/Damaged Card', img: ImgReportCard, onClick: () => handleNavigateToTab('Cards') }, // Fixed handler
    { label: 'Update Photo', img: ImgUpdatePhoto, onClick: handleOpenPhotoModal }, // Updated handler
    { label: 'Add/View Delegate', img: ImgDelegate, onClick: () => handleNavigateToTab('Delegates') }, // Fixed handler
    { label: 'Host And Visitor', img: ImgHostVisitor, onClick: () => handleNavigateToTab('Requests') }, // Fixed handler
  ];

  return (
    <div className="bg-[#f4f1fb] min-h-screen pt-16 pl-20 p-6 text-sm text-[#5a4f72]">
      <h1 className="text-md mb-4">Home</h1>

      {/* Top Icons with Images */}
     {/* Top Icons with Images - Horizontal Layout */}
<div className="grid grid-cols-2 md:grid-cols-6 gap-4 mb-6">
  {items.map((item, idx) => (
    <div
      key={idx}
      className="bg-white p-4 rounded-xl shadow-sm flex items-center gap-4 hover:shadow-md transition"
      onClick={item.onClick} // Attach onClick handler
    >
      <img src={item.img} alt={item.label} className="w-10 h-10" />
      <div className="text-sm font-medium text-[#5a4f72]">{item.label}</div>
    </div>
  ))}
</div>


      {/* Bottom Sections */}
      <div className="grid md:grid-cols-3 gap-4">
      <div className="bg-white rounded-xl shadow-sm p-4">
  <h2 className="text-sm font-semibold mb-4">Quick Links</h2>
  <div className="grid grid-cols-2 gap-3">
    {[
      { label: 'My Profile', tab: 'Identity' },
      { label: 'My Access Areas (6)', tab: 'Access' },
      { label: 'My Pending Tasks (12)', tab: 'Corporate' },
      { label: 'My Pending Audit Tasks (122)', tab: 'Cards' },
      { label: 'Open Visit Requests (0)', tab: 'Requests' },
    ].map((link, idx) => (
      <div
        key={idx}
        className="flex items-center justify-between border px-3 py-3 rounded-lg hover:bg-gray-50 shadow-sm"
        onClick={() => handleNavigateToTab(link.tab)} // Navigate to respective tab
      >
        <span className="text-sm">{link.label}</span>
        <img src={window} alt="icon" className="w-5 h-5" />
      </div>
    ))}
  </div>
</div>
      {/* Recommendations */}
<div className="bg-white rounded-xl shadow-sm p-4">
  <h2 className="text-sm font-semibold mb-4 text-center">Recommendations (0)</h2>
  <div className="flex flex-col items-center justify-center h-48 text-center">
    <img src={Request} alt="Camera Icon" className="w-12 h-12 mb-3" />
    <p className="text-xs text-[#9b9b9b]">
      There Are No Recommendations Available For You At This Time
    </p>
  </div>
</div>


        {/* Announcements */}
        <div className="bg-white rounded-xl shadow-sm p-4 text-center">
          <h2 className="text-sm font-semibold mb-4">Announcements (0)</h2>
        </div>
      </div>

      {/* AddAccessRequest Modal */}
      {isModalOpen && (
        <AddAccessRequest
          onClose={handleCloseModal}
          onAddAccess={(data) => console.log('Access Added:', data)} // Example handler
        />
      )}

      {/* AddCardForm Modal */}
      {isCardModalOpen && (
        <AddCardForm
          onClose={handleCloseCardModal}
          onAdd={(data) => console.log('Card Added:', data)} // Example handler
        />
      )}

      {/* EditPhotoModal */}
      {isPhotoModalOpen && (
        <EditPhotoModal
          onClose={handleClosePhotoModal} // Close modal
          onSave={(imageSrc) => console.log('Photo Updated:', imageSrc)} // Example handler
        />
      )}
    </div>
  );
};

export default Dashboard;
