import React, { useState } from 'react';
import { Formik, Form, ErrorMessage } from 'formik';
import { toast } from 'react-toastify';
import ImageCapture from '../../Components/Global/ImageAndCamera/ImageCaptureForForm';
import Input from '../../Components/Global/Input/Input';
import Button from '../../Components/Global/Button';
import CustomDropdown from '../../Components/Global/CustomDropdown';
import { addDeniedGuest } from "../../api/Appointments";
import { usePatientGuestTypeMasterData } from "../../hooks/usePatientGuestTypeMasterData"; // Import the custom hook
import DateInput from '../../Components/Global/Input/DateInput';
import { useTranslation } from 'react-i18next';

const AddProhibitedGuestForm = ({ appointmentId, onAddGuest, onClose, patient_id }) => {
  const [showForm, setShowForm] = useState(true); // State to toggle form visibility
  const { relationshipTypes } = usePatientGuestTypeMasterData();
  const { t } = useTranslation();

  const handleSubmit = async (values) => {
    if (values) {
      const relationshipIndex = relationshipTypes.indexOf(values.relationship);
    const guestPayload = {
  first_name: values.firstName,
  last_name: values.lastName,
  email: values.guestMail,
  reason: values.denialReason,
  phone: values.mobile,
  relationship_type: relationshipIndex >= 0 ? relationshipIndex : 0,
  patient_id: patient_id,
  guest_type: 2,
  birth_date: values.birth_date 
    ? new Date(values.birth_date).toISOString().split("T")[0]
    : null, // ✅ formatted YYYY-MM-DD or null
};


      try {
        const response = await addDeniedGuest(guestPayload);
        console.log("Denied guest added successfully:", response);
        onAddGuest({ ...values });
        toast.success("Denied guest added successfully!", {
          autoClose: 2000,
          pauseOnHover: false,
        });
        return true; // indicate success
      } catch (error) {
        console.error("Error adding denied guest:", error.response?.data || error.message);
        toast.error(
          error.response?.data?.message || "Failed to add denied guest. Please try again."
        );
        return false;
      }
    } else {
      toast.error("Please enter Guest Name", {
        autoClose: 2000,
        pauseOnHover: false,
      });
      return false;
    }
  };

  // Handle "Add More" button functionality
  const handleAddMore = async (values, resetForm) => {
    const success = await handleSubmit(values);
    if (success) {
      resetForm(); // reset only if addition was successful
    }
  };

  const handleSave = (values) => {
    handleSubmit(values); 
    setShowForm(false); 
    onClose(); 
  };

  return (
    showForm && (
      <div className="modal-overlay">
        <div className="modal-content p-6 bg-white rounded-lg mt-5 border border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-semibold mb-4">{t('inpatient_visit.add_denied_guest')}</h3>
            <button
              type="button"
              className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
              onClick={onClose}
            >
              &times;
            </button>
          </div>
          <Formik
            initialValues={{ firstName: '', lastName: '', mobile: '', guestMail: '', relationship: '', birth_date: '', denialReason: '' }}
            validate={(values) => {
              const errors = {};
              if (!values.firstName) {
                errors.firstName = t('inpatient_visit.first_name_required_error');
              }
              if (!values.lastName) {
                errors.lastName = t('inpatient_visit.last_name_required_error');
              }
              if (!values.denialReason) {
                errors.denialReason = t('inpatient_visit.denial_reason_required_error');
              }
              return errors;
            }}
            onSubmit={handleSubmit}
          >
            {({ setFieldValue, values, resetForm }) => (
              <Form>
                {/* Image section */}
                <div className="mb-4">
                  <ImageCapture
                    onImageCaptured={(img) => setFieldValue('image', img)}
                    onImageUploaded={(img) => setFieldValue('image', img)}
                  />
                </div>

                {/* Form fields */}
                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <Input
                      type="text"
                      name="firstName"
                      value={values.firstName}
                      onChange={(e) => setFieldValue('firstName', e.target.value)}
                      placeholder={t('inpatient_visit.enter_first_name')}
                      label={t('inpatient_visit.first_name_required')}
                      required
                    />
                    <ErrorMessage name="firstName" component="div" className="text-red-500 text-xs mt-1" />
                  </div>
                  <div>
                    <Input
                      type="text"
                      name="lastName"
                      value={values.lastName}
                      onChange={(e) => setFieldValue('lastName', e.target.value)}
                      placeholder={t('inpatient_visit.enter_last_name')}
                      label={t('inpatient_visit.last_name_required')}
                      required
                    />
                    <ErrorMessage name="lastName" component="div" className="text-red-500 text-xs mt-1" />
                  </div>
                  <div>
                    <div className="w-full">
                     <DateInput
  label={t('inpatient_visit.date_of_birth')}
  name="birth_date"
  value={values.birth_date}
  className="h-11"
  onChange={(date) => setFieldValue("birth_date", date)}
  placeholder={t('inpatient_visit.select_date')}
/>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 mb-4">
                  <div>
                    <Input
                      type="text"
                      name="mobile"
                      value={values.mobile}
                      onChange={(e) => setFieldValue('mobile', e.target.value)}
                      placeholder={t('inpatient_visit.enter_phone_number')}
                      label={t('inpatient_visit.phone')}
                    />
                  </div>
                  <div>
                    <Input
                      type="email"
                      name="guestMail"
                      value={values.guestMail}
                      onChange={(e) => setFieldValue('guestMail', e.target.value)}
                      placeholder={t('inpatient_visit.enter_email')}
                      label={t('inpatient_visit.email')}
                    />
                  </div>
                  <div>
                    <Input
                      type="text"
                      name="denialReason"
                      value={values.denialReason}
                      onChange={(e) => setFieldValue('denialReason', e.target.value)}
                      placeholder={t('inpatient_visit.enter_denial_reason')}
                      label={t('inpatient_visit.denial_reason_required')}
                      required
                    />
                    <ErrorMessage name="denialReason" component="div" className="text-red-500 text-xs mt-1" />
                  </div>
                </div>

                <div className="flex gap-4 pl-4 justify-center pb-4">
                  <Button type="primary" onClick={() => handleAddMore(values, resetForm)} label={t('inpatient_visit.add_more')} />
                  <Button type="primary" label={t('inpatient_visit.save')} onClick={() => handleSave(values)} />
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    )
  );
};

export default AddProhibitedGuestForm;