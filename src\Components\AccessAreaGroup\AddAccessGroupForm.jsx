import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import Button from "../../Components/Global/Button";
import Input from "../Global/Input/Input";
import { IoIosArrowBack } from "react-icons/io";
import CustomDropdown from "../../Components/Global/CustomDropdown";

const AddAccessGroupForm = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    name: "",
    groupId: "",
    riskLevel: "Low", // default value   
    status: "Active", // default value
    requestableByAdmin: "Yes", // default value
    requestableInSelfService: "Yes", // default value
    areaSpecialInstruction: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast.error("Please enter the Name");
      return false;
    }
    if (!formData.groupId.trim()) {
      toast.error("Please enter the Group Id");
      return false;
    }
    return true;
  };

  const handleSave = (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    // Get the current date & time (adjust the format if needed)
    const currentDateTime = new Date().toLocaleString();

    // Map the form data to match the table's data structure
    const newAccessGroup = {
      id: Date.now(), // generate a unique ID
      areaGroupName: formData.name,
      type: formData.groupId, // adjust mapping as necessary
      creation: currentDateTime,
      requestable: formData.requestableInSelfService,
      status: formData.status,
    };

    console.log("Final formData before save:", newAccessGroup);

    toast.success("Access group added successfully! Redirecting...", {
      autoClose: 2000,
      pauseOnHover: false,
    });

    setTimeout(() => {
      // Navigate back to the list page and pass the new access group in the state
      navigate("/access-group", { state: { newAccessGroup } });
    }, 0);
  };

  return (
    <>
      <div className="flex items-center gap-2 pl-24 pt-20 text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"    
          onClick={() => navigate("/access-group")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Access Group</h2>
        </div>
      </div>

      <div className="p-6 pl-20">
        <form className="space-y-4" onSubmit={handleSave}>
          <div className="w-full bg-white shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-[10px] p-4 border">
            {/* Name Field */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Name *</label>
              <Input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                placeholder="Enter Name"
              />
            </div>

            {/* Group Id Field */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Group Id *</label>
              <Input
                type="text"
                name="groupId"
                value={formData.groupId}
                onChange={handleInputChange}
                placeholder="Enter Group Id"
              />
            </div>

            {/* Risk Level Dropdown */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Risk Level</label>
              <CustomDropdown
                options={["Low", "Medium", "High"]}
                defaultValue={formData.riskLevel}
                value={formData.riskLevel}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, riskLevel: value }))
                }
              />
            </div>

            {/* Status Dropdown */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Status</label>
              <CustomDropdown
                options={["Active", "Inactive"]}
                defaultValue={formData.status}
                value={formData.status}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, status: value }))
                }
              />
            </div>

            {/* Requestable by Admin Dropdown */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Requestable by Admin</label>
              <CustomDropdown
                options={["Yes", "No"]}
                defaultValue={formData.requestableByAdmin}
                value={formData.requestableByAdmin}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, requestableByAdmin: value }))
                }
              />
            </div>

            {/* Requestable in Self Service Dropdown */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Requestable in Self Service</label>
              <CustomDropdown
                options={["Yes", "No"]}
                defaultValue={formData.requestableInSelfService}
                value={formData.requestableInSelfService}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({
                    ...prev,
                    requestableInSelfService: value,
                  }))
                }
              />
            </div>

            {/* Area Special Instruction Field */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Area Special Instruction</label>
              <textarea
                name="areaSpecialInstruction"
                value={formData.areaSpecialInstruction}
                onChange={handleInputChange}
                className="border rounded-md w-full p-2 focus:ring-1 outline-none focus:ring-[#4F2683] h-20"
                placeholder="Enter special instructions for the area"
              />
            </div>
          </div>

          <div className="flex justify-center gap-1 mt-6">
            <Button
              type="cancel"
              buttonType="button"
              label="Cancel"
              onClick={() => navigate("/access-group")}
            />
            <Button type="primary" buttonType="submit" label="Save" />
          </div>
        </form>
      </div>
    </>
  );
};

export default AddAccessGroupForm;
