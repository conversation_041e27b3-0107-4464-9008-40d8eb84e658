import React, { useState, useMemo } from "react";
import GenericTable from "../../../GenericTable";
import TruncatedCell from "../../../Tooltip/TruncatedCell";
import TruncatedRow from "../../../Tooltip/TrucantedRow";

function Requests() {
  const [searchTerm, setSearchTerm] = useState("");

  const requestsData = [
    {
      requestId: "177defrghtjyukjyhtgrf24260",
      type: "Request Access",
      justification: "General Access",
      requestedBy: "AKSHAY SARDA",
      startDate: "04-Dec-2024",
      endDate: "-",
      items: 1,
      createdOn: "04-Dec-2024",
    },
    
  ];

  const columns = [
    { name: <TruncatedCell text="Request ID"/>, 
        selector: (row) => row.requestId,
        cell:(row) => <TruncatedRow text={row.requestId} />,
             sortable: true },
    { name: "Type", selector: (row) => row.type },
    { name: "Justification", selector: (row) => row.justification },
    { name: "Requested By", selector: (row) => row.requestedBy },
    { name: "Start/Active Date", selector: (row) => row.startDate },
    { name: "End/Removal Date", selector: (row) => row.endDate },
    { name: "Items", selector: (row) => row.items },
    { name: "Created On", selector: (row) => row.createdOn },
  ];

  const filteredData = useMemo(() => {
    if (!searchTerm) return requestsData;
    return requestsData.filter((item) =>
      Object.values(item).some(
        (value) =>
          typeof value === "string" &&
          value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  }, [requestsData, searchTerm]);

  return (
    <div className="bg-white rounded-lg shadow-md">
      <div className="overflow-x-auto"> {/* Added scrollable container */}
        <GenericTable
          title={"Requests"}
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          columns={columns}
          data={filteredData}
          fixedHeader
          fixedHeaderScrollHeight="400px"
          highlightOnHover
          striped
        />
      </div>
    </div>
  );
}

export default Requests;