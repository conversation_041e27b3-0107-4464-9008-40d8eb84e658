import React, { useState, useEffect } from "react";
import Input from "../Global/Input";

function ViewCommentsEditModal({ commentData, onUpdate, onClose }) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [formData, setFormData] = useState({
    comment: commentData.comment || "",
    addedBy: commentData.addedBy || "",
    addedOn: commentData.addedOn || ""
  });

  useEffect(() => {
    setFormData({
      comment: commentData.comment || "",
      addedBy: commentData.addedBy || "",
      addedOn: commentData.addedOn || ""
    });
  }, [commentData]);

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const handleSave = (e) => {
    e.preventDefault();
    onUpdate(formData);
    setIsEditMode(false);
  };

  return (
    <div className="w-full p-2">
      <div className="flex items-center mb-2 px-2 justify-between">
        <h2 className="text-[30px] font-normal text-[#4F2683]">
          Comments
        </h2>
        <button
          className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
          type="button"
          onClick={onClose}
        >
          &times;
        </button>
      </div>
      <hr className="mx-3" />
      <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
        <div className="flex items-center mb-4">
          <label htmlFor="comment" className="w-1/4 text-[16px] font-normal">
            Comment
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="comment"
              id="comment"
              value={formData.comment}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        <div className="flex items-center mb-4">
          <label htmlFor="addedBy" className="w-1/4 text-[16px] font-normal">
            Added by
          </label>
          <div className="w-3/4">
            <Input
              type="text"
              name="addedBy"
              id="addedBy"
              value={formData.addedBy}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        <div className="flex items-center mb-4">
          <label htmlFor="addedOn" className="w-1/4 text-[16px] font-normal">
            Added On
          </label>
          <div className="w-3/4">
            <Input
              type="date"
              name="addedOn"
              id="addedOn"
              value={formData.addedOn}
              onChange={handleChange}
              disabled={!isEditMode}
              className={inputClassName}
            />
          </div>
        </div>

        <div className="flex gap-4 justify-end">
          {!isEditMode ? (
            <button
              type="button"
              onClick={() => setIsEditMode(true)}
              className="px-4 py-2 bg-[#4F2683] text-white rounded"
            >
              Edit
            </button>
          ) : (
            <>
              <button
                type="button"
                onClick={() => {
                  setIsEditMode(false);
                  setFormData({
                    comment: commentData.comment || "",
                    addedBy: commentData.addedBy || "",
                    addedOn: commentData.addedOn || ""
                  });
                }}
                className="px-4 py-2 bg-gray-400 text-white rounded"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Save
              </button>
            </>
          )}
        </div>
      </form>
    </div>
  );
}

export default ViewCommentsEditModal;
