import React, { useState, useEffect } from "react";
import Input from "../../Global/Input/Input";
import { updatePatientFriend } from '../../../api/PatientHub';
import CustomDropdown from "../../Global/CustomDropdown";
import { usePatientGuestTypeMasterData } from '../../../hooks/usePatientGuestTypeMasterData';
function ViewEditModal({ familyData, onUpdate, onClose }) {
  const [isEditMode, setIsEditMode] = useState(false);
  const [show, setShow] = useState(false);

  // Get relationship types from master data
  const { relationshipTypes } = usePatientGuestTypeMasterData();

  // Build dropdown options directly from the API data
  const relationshipOptions = relationshipTypes.map((t) => ({
    label: t.value,   // e.g. "Spouse"
    value: t.key,      // e.g. 0
  }));

  // DEBUG: make sure you see all 4 options in the console
  console.log("ViewEditModal - Dropdown options:", relationshipOptions);

  // Helper function to get relationship label by key
  const getRelationshipLabel = (relationshipKey) => {
    if (!relationshipKey || !relationshipTypes.length) return "";
    const relationship = relationshipTypes.find(type => type.key == relationshipKey);
    return relationship ? relationship.value : "";
  };

  const [formData, setFormData] = useState({
    id: familyData.id || "",
    name: familyData.name || "",
    lastName: familyData.lastName || "",
    relationship: familyData.relationshipKey || familyData.relationship || "",
    phone: familyData.phone || "",
    email: familyData.email || ""
  });

  useEffect(() => {
    console.log("ViewEditModal - familyData received:", familyData);
    console.log("ViewEditModal - familyData.relationshipKey:", familyData.relationshipKey);
    console.log("ViewEditModal - familyData.relationship:", familyData.relationship);

    const relationshipValue = familyData.relationshipKey || familyData.relationship || "";
    console.log("ViewEditModal - Setting relationship value to:", relationshipValue);

    setFormData({
      id: familyData.id || "",
      name: familyData.name || "",
      lastName: familyData.lastName || "",
      relationship: relationshipValue,
      phone: familyData.phone || "",
      email: familyData.email || ""
    });
  }, [familyData]);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  const inputClassName = `w-full border bg-transparent rounded p-2 ${
    isEditMode ? "focus:outline-none" : "border-none text-[#8F8F8F]"
  }`;

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };
const handleSave = async (e) => {
  e.preventDefault();

  console.log("ViewEditModal - formData before processing:", formData);
  console.log("ViewEditModal - formData.relationship type:", typeof formData.relationship);
  console.log("ViewEditModal - formData.relationship value:", formData.relationship);

  // Validate that relationship is selected and is a valid number
  const relationshipType = parseInt(formData.relationship, 10);
  if (isNaN(relationshipType)) {
    console.error("Invalid relationship type selected");
    alert("Please select a valid relationship type");
    return;
  }

  const payload = {
    first_name: formData.name,
    last_name: formData.lastName,
    relationship_type: relationshipType, // Use the validated number
    phone: formData.phone,
    email: formData.email,
  };

  console.log("ViewEditModal - Final payload:", payload);
  console.log("ViewEditModal - relationship_type type:", typeof payload.relationship_type);
  console.log("ViewEditModal - relationship_type value:", payload.relationship_type);

  try {
    const updated = await updatePatientFriend(formData.id, payload);

    onUpdate({
      id: formData.id,
      name: updated.first_name,
      lastName: updated.last_name,
      relationship_type: updated.relationship_type || relationshipType,
      phone: updated.phone,
      email: updated.email,
    });
    setIsEditMode(false);
  } catch (err) {
    console.error("Update failed:", err);
  }
 };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`w-full max-w-3xl bg-white rounded-lg shadow-lg h-full p-0 transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center mb-2 px-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Friends & Family
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSave} className="bg-white p-6 rounded-lg">
          <div className="flex items-center mb-4">
            <label htmlFor="name" className="w-1/4 text-[16px] font-normal">
              First Name
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="name"
                id="name"
                value={formData.name}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label htmlFor="lastName" className="w-1/4 text-[16px] font-normal">
              Last Name
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="lastName"
                id="lastName"
                value={formData.lastName}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label
              htmlFor="relationship"
              className="w-1/4 text-[16px] font-normal"
            >
              Relationship
            </label>
            <div className="w-3/4">
              {!isEditMode ? (
                <Input
                  type="text"
                  name="relationship"
                  id="relationship"
                  value={getRelationshipLabel(formData.relationship)}
                  disabled
                  className={inputClassName}
                />
              ) : (
                <CustomDropdown
                  className="h-11 rounded"
                  placeholder="Select Relationship"
                  options={relationshipOptions}
                  onSelect={(selectedValue) => {
                    console.log("ViewEditModal - Selected relationship value:", selectedValue);
                    console.log("ViewEditModal - Selected relationship type:", typeof selectedValue);
                    setFormData((prevData) => ({
                      ...prevData,
                      relationship: selectedValue, // Store the numeric key directly
                    }));
                  }}
                  value={formData.relationship ? parseInt(formData.relationship, 10) : null}
                  bgColor="bg-[white] text-black"
                  textColor="text-black"
                  hoverBgColor="hover:bg-[#4F2683]"
                  borderColor="border-gray-300"
                />
              )}
            </div>
          </div>
          <div className="flex items-center mb-4">
            <label htmlFor="phone" className="w-1/4 text-[16px] font-normal">
              Phone
            </label>
            <div className="w-3/4">
              <Input
                type="text"
                name="phone"
                id="phone"
                value={formData.phone}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>

          <div className="flex items-center mb-4">
            <label htmlFor="email" className="w-1/4 text-[16px] font-normal">
              Email
            </label>
            <div className="w-3/4">
              <Input
                type="email"
                name="email"
                id="email"
                value={formData.email}
                onChange={handleChange}
                disabled={!isEditMode}
                className={inputClassName}
              />
            </div>
          </div>

          <div className="flex gap-4 justify-end">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={() => {
                    setIsEditMode(false);
                    setFormData({
                      id: familyData.id || "",
                      name: familyData.name || "",
                      lastName: familyData.lastName || "",
                      relationship: familyData.relationshipKey || familyData.relationship || "",
                      email: familyData.email || "",
                      phone: familyData.phone || ""
                    });
                  }}
                  className="px-4 py-2 bg-gray-400 text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
      </div>
    </div>
  );
}

export default ViewEditModal;
