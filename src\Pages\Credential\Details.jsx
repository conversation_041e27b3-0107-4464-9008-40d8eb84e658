import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { IoIosArrowBack } from "react-icons/io";
import { useTranslation } from 'react-i18next';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import HistoryTable from '../../Components/Observation/HistoryTable';
import userImg from "../../Images/fromimg.svg";
import ViewRequest from '../../Components/Credential/Credential/ViewRequest';
import Task from '../../Components/Credential/Credential/Task';

const CredentialDetails = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const [selectedTab, setSelectedTab] = useState(t('credential_details.tab_view_request'));
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  // const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc);
    setIsModalOpen(false);
  };

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-24 pt-16">
      <div className="flex items-center pt-1 text-[#4F2683]">
        <div
          className='flex items-center gap-1 cursor-pointer'
          onClick={() => navigate('/credential')}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('credential_details.title')}</h2>
        </div>
      </div>

      {/* Profile Section */}
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        profileImage={profileImage}
        defaultImage={userImg}
        name={t('credential_details.name')}
        additionalFields={[
          { label: t('credential_details.eid'), value: "300074045" },
          { label: t('credential_details.created_on'), value: "20-Mar-2025 12:00 AM" },
          { label: t('credential_details.status'), value: t('credential_details.status_unprinted_badges') },
        ]}
      />

      <div className="flex gap-8">
        <div className="w-1/12 mt-2">
          {[t('credential_details.tab_view_request'), t('credential_details.tab_task')].map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left font-normal p-2 mb-2 ${
                selectedTab === tab
                  ? 'text-[#4F2683] text-[18px] border-l-2 border-[#4F2683]'
                  : 'text-[18px] text-gray-700'
              }`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-11/12 ml-4">
          {selectedTab === t('credential_details.tab_view_request') && <ViewRequest />}
          {selectedTab === t('credential_details.tab_task') && <Task />}
        </div>
      </div>

      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)}
      />
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default CredentialDetails;
