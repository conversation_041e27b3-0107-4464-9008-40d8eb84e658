import React, { useRef } from 'react';
import Webcam from 'react-webcam';
import { IoIosCamera, IoMdPhotos } from "react-icons/io";
import { RiDeleteBinFill } from "react-icons/ri";
import Button from '../Button';
import demoimg from '../../../Images/demoimg.svg';
const ImageCapture = ({ onImageCaptured, onImageUploaded, onClose, handleSave }) => {
  const webcamRef = useRef(null);
  const [showCamera, setShowCamera] = React.useState(false);
  const [imageSrc, setImageSrc] = React.useState(null);

  const captureImage = () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        setImageSrc(imageSrc);
        onImageCaptured(imageSrc);
        setShowCamera(false);
      }
    }
  };
  const deletePhoto = () => {
    setImageSrc("")
  }
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageSrc(reader.result);
        onImageUploaded(reader.result);
        setShowCamera(false);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className=" flex flex-col  ">
      <div className='flex justify-between border-b-2 pb-3 items-center '>
        <h2 className="text-[#4F2683] text-xl font-normal ">Edit Photo</h2>
        <Button
          type="close"
          rounded={true}
          className="py-1 px-1"
          onClick={onClose}
          label={<span className="text-xl">&times;</span>}
        />
      </div>
      {showCamera ? (
        <div className="flex flex-col items-center pt-4 justify-center">
          <div className=''>
            <Webcam
              audio={false}
              ref={webcamRef}
              screenshotFormat="image/jpeg"
              videoConstraints={{ facingMode: "user" }}
              className="rounded-full object-cover w-[150px] h-[150px] sm:w-[200px] sm:h-[200px] max-w-full"
            />
          </div>
        </div>
      ) : (
        <div>
          <div className="flex flex-col pt-6 items-center relative">
            <img
              src={imageSrc || demoimg}
              alt="Patient"
              className=" w-[150px] h-[150px] sm:w-[200px] sm:h-[200px] max-w-full rounded-full object-cover border border-gray-300"
            />
          </div>
        </div>
      )}
      <div className='pt-4'>
        <input
          id="fileInput"
          type="file"
          accept="image/*"
          className="hidden"
          onChange={handleImageUpload}
        />
      </div>
      <div className='flex justify-between mt-2 border-t-2 pt-4'>
        <div className='flex gap-4'>
          {!showCamera && !imageSrc  && (
            <>
              <button
                type="button"
                className="flex flex-col items-center text-[#4F2683]"
                onClick={() => setShowCamera(true)}
              >
                <IoIosCamera className="text-2xl text-[#4F2683]" />
                <span>Add Photo</span>
              </button>
              <button
                type="button"
                className="flex flex-col items-center text-[#4F2683]"
                onClick={() => document.getElementById('fileInput').click()}
              >
                <IoMdPhotos className="text-2xl text-[#4F2683]" />
                <span>Gallery</span>
              </button>
            </>
          )}

          {/* Show Capture Button Only When Camera is ON */}
          {showCamera && (
            <button
              type="button"
              className="flex flex-col items-center text-[#4F2683]"
              onClick={captureImage}
            >
              <IoIosCamera className="text-2xl text-[#4F2683]" />
              <span>Capture</span>
            </button>
          )}
          {imageSrc &&(
            <Button
              type="primary"
              label="Save Photo"
              rounded="true"
              className="px-8 py-1"
              onClick={handleSave}
            />
          )}
        </div>
        <div>
          {imageSrc&&(
            <button
            type="button"
            className="flex flex-col items-center text-[#333333]"
            onClick={deletePhoto}
            >
            <RiDeleteBinFill className="text-2xl text-[#E21B1B]" /><span>Delete</span>
          </button>
          )}
        </div>
      </div>
    </div>
  );
};

export default ImageCapture;
