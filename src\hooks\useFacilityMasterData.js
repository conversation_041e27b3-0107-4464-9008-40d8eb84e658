import { useEffect, useState, useCallback, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache (persists until page reload)
let facilityMasterDataCache = null;

export const useFacilityMasterData = () => {
  const [masterData, setMasterData] = useState(
    facilityMasterDataCache || { facility_status: [], facility_type: [] }
  );

  const fetchMasterData = useCallback(async () => {
    if (facilityMasterDataCache) return; // Already cached
    try {
      const res = await getMasterData({
        groups: ["facility_status", "facility_type"],
      });
      facilityMasterDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching facility master data");
    }
  }, []);

  useEffect(() => {
    if (!facilityMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  const facilityStatusOptions = useMemo(() => {
    return masterData.facility_status.map((item) => ({
      label: item.value,
      value: item.key,
    }));
  }, [masterData.facility_status]);

  const facilityTypeOptions = useMemo(() => {
    return masterData.facility_type.map((item) => ({
      label: item.value,
      value: item.key,
    }));
  }, [masterData.facility_type]);

  return { masterData, facilityStatusOptions, facilityTypeOptions };
};
