import React, { useEffect, useState } from "react";
import Button from "./Button";
import { getAppointmentById, fetchAllGuests } from "../../api/Appointments";
import demoimg from '../../Images/demoimg.svg';

const InfoRow = ({ label, value, extra }) => (
  <div className="flex items-center mt-2">
    <span className="basis-24 font-poppins text-[14px] font-normal leading-[24px]">{label}</span>
    <div className="flex items-center font-poppins text-[14px] font-normal leading-[24px] ml-6">
      <span>{value}</span>
      <span className="pl-2 font-poppins text-[14px] font-normal leading-[24px]">{extra && extra}</span>
    </div>
  </div>
);

const PatientCard = ({ appointmentId, data: initialData, setPatientId = () => {}, hideFields = false, handelViewClick, viewAll = false  }) => {
  const [data, setData] = useState(null);
const handleViewGuests = async () => {

  handelViewClick({appointment_guest_status: 1,})
  // try {
    
  //   console.log("Params sent to API:", {
  //     appointmentId,
  //     guest_type: 1,
  //     patient_id: data.patient_id,
  //     appointment_guest_status: 1,
  //   });

  //   const guests = await fetchAllGuests(
  //     appointmentId, // appointment_id
  //     0,           // guest_type 
  //     data.patient_id,
  //     1,             // appointment_guest_status (Confirmed)
  //     null,          // sortBy
  //     null           // sortOrder
  //   );
    
  //   console.log("API Response:", guests);
  // } catch (error) {
  //   console.error("Error:", error.response?.data || error.message);
  // }
};

  useEffect(() => {
    if (!initialData && appointmentId) {
      getAppointmentById(appointmentId).then(res => {
        setData(res.data);
        setPatientId(res?.data?.patient_id);
      });
    }
  }, [appointmentId]);

  if (!data) return <div>Loading...</div>;

  return (
    <div className="bg-[#F1EEF5] p-2 mt-4 pl-2 md:p-2 rounded-lg shadow-md mb-8 md:ps-5 md:pb-4 ">
      <div className="flex justify-between items-center">
        <h2 className="text-[#4F2683] mb-1 text-[22px] font-medium leading-[33px] ">
          {data.first_name} {data.last_name}
        </h2>
      </div>

      <div className="flex gap-3 items-start">
        <div className="mt-1 mr-4 w-[174px] h-[174px] bg-blue-200 rounded-full flex-shrink-0 overflow-hidden">
          <img
            src={data.image || demoimg}
            alt="patient"
            className="w-full h-full object-cover"
          />
        </div>

        <div className="flex-1 text-xs text-gray-700 space-y-0.5">
          <InfoRow label="MRN" value={data.mrn} />
          {!hideFields && (
            <>
              <InfoRow label="Department" value={data.department} />
              <InfoRow label="Room" value={data.room_number} extra={<span className="ms-32">Bed: {data.beds}</span>} />
              <InfoRow label="Physician" value={data.provider_name} />
            </>
          )}
          <InfoRow label="Status" value={data.appointment_status_name} />
          <InfoRow label="Type" value={data.appointment_type_name} />
           {!hideFields &&(<InfoRow
        label="Max Visitors"
        value={`${data.checked_in_visitors} of ${data.max_allowed_visitors}`}
        extra={
          <Button
            type="underline"
            className="pl-1 ms-32 p-0 text-xs hover:text-purple-600 text-[#4F2683]"
            label={viewAll ? "View All Guests" : "View Guests"}
            onClick={() => handelViewClick({ 
              appointment_guest_status: viewAll ? null : 1 
            })}
          />
            }
          />
          )}
        </div>
      </div>
    </div>
  );
};

export default PatientCard;