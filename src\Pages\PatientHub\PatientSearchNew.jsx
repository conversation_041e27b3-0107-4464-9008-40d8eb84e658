import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import SearchBar from "../../Components/Global/SearchBar";
import demoimg from "../../Images/demoimg.svg";
import formatDateTime from "../../utils/formatDateTime";
import { searchPatients } from "../../api/PatientHub";
import { getMediaByModel } from "../../api/global";

const PatientSearchNew = ({
  placeholder,
  searchTerm,
  onInputChange,
  onSearchSubmit,
  results,
  onResultClick,
  isDropdownVisible,
  containerRef,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [patientImages, setPatientImages] = useState({});


  // Fetch real URLs for any image IDs in `results`
  useEffect(() => {
    if (!results?.length) return;

    const fetchImages = async () => {
      const updated = {};
      for (const p of results) {
        const imageId = p.image;
        if (!imageId) {
          // no image provided
          updated[p.id] = demoimg;
          continue;
        }
        // if it's already a URL, keep it
        if (typeof imageId === "string" && imageId.startsWith("http")) {
          updated[p.id] = imageId;
          continue;
        }
        // else assume it's an ID and fetch
        try {
          const media = await getMediaByModel("Patient", {
            key: "image",
            value: imageId,
          });
          updated[p.id] = media.value;
        } catch (err) {
          console.error("Error fetching patient image:", err);
          updated[p.id] = demoimg;
        }
      }
      setPatientImages(updated);
    };

    fetchImages();
  }, [results]);

  const handleSearchSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    try {
      const searchResults = await searchPatients({ query: searchTerm });
      onSearchSubmit(searchResults);
    } catch (error) {
      console.error("Error fetching search results:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="w-full sm:w-auto relative" ref={containerRef}>
      <form onSubmit={handleSearchSubmit}>
        <SearchBar
          placeholder={placeholder || t('patient_hub.search_patient_placeholder')}
          iconSrc={""}
          onInputChange={onInputChange}
          value={searchTerm}
          borderColor="#4F2683"
        />
      </form>

      {loading && <p className="text-gray-500 text-sm">{t('patient_hub.loading')}</p>}

      {isDropdownVisible && (
        <div
          className="w-96 mt-2 border absolute p-2 bg-white z-10 rounded-md shadow-lg overflow-y-auto left-1/2 transform -translate-x-1/2"
          style={{ maxHeight: "200px" }}
        >
          {results.map((patient) => (
            <div
              key={patient.id}
              className="flex items-center gap-2 p-2 border-b cursor-pointer hover:bg-gray-100"
              onClick={() => onResultClick(patient)}
            >
              <img
                src={patientImages[patient.id] || demoimg}
                alt={`${patient.first_name} ${patient.last_name}`}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <h2 className="font-semibold">
                  {`${patient.first_name} ${patient.last_name}`}
                </h2>
                <div className="flex flex-row gap-1">
                  <p className="text-[12px] text-gray-600">
                    {formatDateTime(patient.birth_date)}, {t('patient_hub.mrn')}: {patient.mrn}
                  </p>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PatientSearchNew;
