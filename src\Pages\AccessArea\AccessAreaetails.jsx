import React, { useState } from 'react';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import userImg from '../../Images/Building.svg'
import { useNavigate } from 'react-router-dom';
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from '../../Components/Observation/HistoryTable';
import AreaDetails from '../../Components/AccessArea/AreaDetails';
import AreaFacility from '../../Components/AccessArea/AreaFacility';
import OwnersApprovers from '../../Components/AccessArea/OwnersApprovers';
import PerimeterAreas from '../../Components/AccessArea/PerimeterAreas';
import AssignedIdentities from '../../Components/AccessArea/AssignedIdentities';
import { useTranslation } from 'react-i18next';

const AccessAreaDetails = () => {
  const { t } = useTranslation();
  const [selectedTab, setSelectedTab] = useState(t('access_area_details.tab_area_details'));

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const navigate = useNavigate();

  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  // Function to handle image capture
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc); 
    setIsModalOpen(false);
  };

  const tabKeys = [
    'access_area_details.tab_area_details',
    'access_area_details.tab_facility',
    'access_area_details.tab_owners_approvers',
    'access_area_details.tab_perimeter_areas',
    'access_area_details.tab_assigned_identities',
  ];

  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div className='flex items-center gap-1 cursor-pointer' onClick={() => navigate('/access-areas')}>
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">{t('access_area_details.title')}</h2>
        </div>
      </div>
      <DetailsCard
        OpenPhotoModal={() => setIsModalOpen(true)}
        handleHistoryOpen={handleHistoryOpen}
        profileImage={profileImage}
        defaultImage={userImg}
        name={t('access_area_details.area_name')}
        showHistoryButton={true}
        additionalFields={[
          { label: t('access_area_details.creation_date'), value: "05-03-2020" },
          { label: t('access_area_details.area_type'), value: "Data Center" },
          { label: t('access_area_details.requestable_in_self_service'), value: "Yes" }
        ]}
      />

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-4">
          {tabKeys.map((tabKey) => (
            <button
              key={tabKey}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === t(tabKey) ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(t(tabKey))}
            >
              {t(tabKey)}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] ">
          {selectedTab === t('access_area_details.tab_area_details') && <AreaDetails />}
          {selectedTab === t('access_area_details.tab_facility') && <AreaFacility />}
          {selectedTab === t('access_area_details.tab_owners_approvers') && <OwnersApprovers />}
          {selectedTab === t('access_area_details.tab_perimeter_areas') && <PerimeterAreas />}
          {selectedTab === t('access_area_details.tab_assigned_identities') && <AssignedIdentities />}
        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)} />
      {isModalOpen && (
        <EditPhotoModal
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default AccessAreaDetails;
