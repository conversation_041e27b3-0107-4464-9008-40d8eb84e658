import React, { useState } from "react";
import GenericTable from "../GenericTable";
import AssignedIdentitiesAdd from "./AssignedIdentitiesAdd";
import Delete from "../../Images/Delete.svg";
import ViewEditAreaFacilityForm from "../Facility/AccessArea/ViewEditAccessAreaForm"; 
import TruncatedCell from "../Tooltip/TruncatedCell";
import TruncatedRow from "../Tooltip/TrucantedRow";

const AssignedIdentities = () => {
  const [data, setData] = useState([
    {
      id: 1,
      name: "<PERSON>",
      eid: "E001",
      type: "Employee",
      company: "Company A",
      organization: "Org1",
      jobTitle: "Developer",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      id: 2,
      name: "<PERSON>",
      eid: "E002",
      type: "Manager",
      company: "Company B",
      organization: "Org2",
      jobTitle: "Team Lead",
      endDate: "2023-11-30",
      status: "Inactive",
    },
    {
      id: 3,
      name: "<PERSON>",
      eid: "E003",
      type: "Employee",
      company: "Company A",
      organization: "Org1",
      jobTitle: "Designer",
      endDate: "2023-10-15",
      status: "Active",
    },
    {
      id: 4,
      name: "Bob Brown",
      eid: "E004",
      type: "Director",
      company: "Company C",
      organization: "Org3",
      jobTitle: "Project Manager",
      endDate: "2023-09-30",
      status: "Active",
    },
    {
      id: 5,
      name: "Charlie Green",
      eid: "E005",
      type: "Employee",
      company: "Company D",
      organization: "Org4",
      jobTitle: "QA Engineer",
      endDate: "2023-08-31",
      status: "Active",
    },
  ]);

  const [searchTerm, setSearchTerm] = useState("");
  const [showAddForm, setShowAddForm] = useState(false);
  const [showViewForm, setShowViewForm] = useState(false);
  const [selectedIdentity, setSelectedIdentity] = useState(null);

  const columns = [
    {
      name: "Name",
      selector: (row) => row.name,
      cell:(row) => <TruncatedRow text={row.name}/>,
      sortable: true,
    },
    {
      name: "EID",
      selector: (row) => row.eid,
      cell:(row) => <TruncatedRow text={row.eid}/>,
    },
    {
      name: "Type",
      selector: (row) => row.type,
      cell:(row) => <TruncatedRow text={row.type}/>,
    },
    {
      name: <TruncatedCell text="Company"/>,
      selector: (row) => row.company,
      cell:(row) => <TruncatedRow text={row.company}/>,
    },
    {
      name:<TruncatedCell text="Organization"/>,
      selector: (row) => row.organization,
      cell:(row) => <TruncatedRow text={row.organization}/>,
    },
    {
      name:<TruncatedCell text="Job Title"/>,
      selector: (row) => row.jobTitle,
      cell:(row) => <TruncatedRow text={row.jobTitle}/>,
    },
    {
      name:<TruncatedCell text="End Date"/>,
      selector: (row) => row.endDate,
      cell:(row) => <TruncatedRow text={row.endDate}/>,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
            row.status.toLowerCase() === "active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
    {
      name: "Action",
      cell: (row) => (
        <img src={Delete} alt="Delete" 
         className="p-1.5 bg-[#F0EDF5] rounded-lg w-8 h-8 cursor-pointer" onClick={() => handleDelete(row.id)} />
      ),
    },
  ];

  const handleAdd = () => {
    setShowAddForm(true);
  };

  const handleCloseAddModal = () => {
    setShowAddForm(false);
  };

  const handleView = (row) => {
    setSelectedIdentity(row);
    setShowViewForm(true);
  };

  const handleUpdate = (updatedIdentity) => {
    setData((prevData) =>
      prevData.map((item) =>
        item.id === updatedIdentity.id ? { ...item, ...updatedIdentity } : item
      )
    );
    setShowViewForm(false);
  };

  const handleCloseViewModal = () => {
    setShowViewForm(false);
  };

  const handleDelete = (id) => {
    setData((prevData) => prevData.filter((item) => item.id !== id));
  };

  return (
    <div className="bg-white rounded-[10px]">
      <GenericTable
        title="Assigned Identities"
        searchTerm={searchTerm}
        showSearch={true}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        onAdd={handleAdd}
        columns={columns}
        data={data}
        showAddButton={true}
      />
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg">
          <div className="rounded-lg max-h-[90vh] ">
              <AssignedIdentitiesAdd
                onClose={handleCloseAddModal}
                onSubmit={(newIdentity, action) => {
                  const identityToAdd = { ...newIdentity, id: Date.now() };
                  setData([identityToAdd, ...data]);
                  if (action === "add") {
                    setShowAddForm(false);
                  }
                }}
                availableIdentities={data}
              />
            </div>
          </div>
        </div>
      )}
      {showViewForm && selectedIdentity && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-10 flex justify-center items-center">
          <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              {/* Assuming you have a similar view/edit form */}
              <ViewEditAreaFacilityForm
                onClose={handleCloseViewModal}
                facilityData={selectedIdentity}
                onUpdate={handleUpdate}
              />
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AssignedIdentities;
