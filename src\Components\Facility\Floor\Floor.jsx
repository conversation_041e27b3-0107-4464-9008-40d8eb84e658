import React, { useState, useRef, useEffect, useCallback } from "react";
import { useParams } from "react-router-dom";
import GenericTable from "../../GenericTable";
import AddFloorForm from "./AddFloorFrom.jsx"; 
import ViewEditFloorForm from "./ViewEditFloorForm";
import { Tooltip } from "react-tooltip";
import { getFloorsByBuilding } from "../../../api/facility";
import Loader from "../../Loader.jsx";

const TruncatedCell = ({ text }) => {
  const cellRef = useRef(null);
  const [isOverflowing, setIsOverflowing] = useState(false);

  useEffect(() => {
    if (cellRef.current) {
      setIsOverflowing(cellRef.current.scrollWidth > cellRef.current.clientWidth);
    }
  }, [text]);

  return (
    <div
      ref={cellRef}
      className="truncate max-w-[150px] overflow-hidden whitespace-nowrap"
      data-tooltip-id={isOverflowing ? `tooltip-${text}` : undefined}
      data-tooltip-content={isOverflowing ? text : undefined}
    >
      {text}
      {isOverflowing && (
        <Tooltip
          id={`tooltip-${text}`}
          className="!bg-[#3D156F] !text !p-1 mt-[-4%] !font-medium"
          place="top"
          effect="solid"
        />
      )}
    </div>
  );
};

const Floor = ({facility}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [floors, setFloors] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errorMsg, setErrorMsg] = useState("");
  const [showAddFloorForm, setShowAddFloorForm] = useState(false);
  const [showViewFloorForm, setShowViewFloorForm] = useState(false);
  const [selectedFloor, setSelectedFloor] = useState(null);

  const fetchFloors = useCallback(async () => {
    if (!facility) {
      setErrorMsg("Building ID is missing.");
      return;
    }
    setLoading(true);
    setErrorMsg("");
    try {
      const res = await getFloorsByBuilding(facility.facility_id, { page: 1, limit: 10 });
      // Extract the floors array from the nested response structure
      const floorsData = res.data && res.data.data ? res.data.data : res.data;
      setFloors(floorsData);
    } catch (error) {
      console.error("Error fetching floors:", error);
      setErrorMsg("Failed to load floors. Please try again later.");
    } finally {
      setLoading(false);
    }
  }, [facility]);

  useEffect(() => {
    if (facility) {
      fetchFloors();
    } else {
      setErrorMsg("Building ID is not provided in the URL.");
    }
  }, [facility, fetchFloors]);

  const columns = [
    {
      name: "Floor ID",
      selector: (row) => row.floor_id,
      cell: (row) => (
        <span className="underline cursor-pointer" onClick={() => handleView(row)}>
          {row.floor_id}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Facility ID",
      selector: (row) => row.facility_id,
      sortable: true,
    },
    {
      name: "Building",
      selector: (row) => (row.building ? row.building.name : row.building_id),
      cell: (row) => <TruncatedCell text={row.building ? row.building.name : row.building_id} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Floor Number" />,
      selector: (row) => row.floor_number,
      cell: (row) => <TruncatedCell text={String(row.floor_number)} />,
      sortable: true,
    },
    {
      name: "Status",
      selector: (row) => row.floor_status_name.value,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
            row.floor_status_name.key === 0
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.floor_status_name.value}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Total Sq Ft",
      selector: (row) => row.total_square_footage,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Max Occupancy" />,
      selector: (row) => row.max_occupancy,
      cell: (row) => <TruncatedCell text={String(row.max_occupancy)} />,
      sortable: true,
    },
    {
      name: <TruncatedCell text="Occupancy Type" />,
      selector: (row) => row.floor_occupancy_type_name.value,
      cell: (row) => <TruncatedCell text={row.floor_occupancy_type_name.value} />,
      sortable: true,
    },
  ];

  const handleView = (floor) => {
    setSelectedFloor(floor);
    setShowViewFloorForm(true);
  };

  const tableTitle = (
    <div className="flex items-center">
      <span>Floor Details</span>
      <span
        data-tooltip-id="floor-header-tooltip"
        data-tooltip-content="This table displays floor details including floor number, status, and more."
        className="ml-2 text-blue-500 cursor-pointer"
      >
        <i className="fas fa-info-circle" />
      </span>
    </div>
  );

  const filteredFloors =
    Array.isArray(floors) && floors.length > 0
      ? floors.filter((floor) =>
          Object.values(floor || {}).some((val) =>
            String(val).toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : [];

  return (
    <div className="bg-white rounded-[10px]">
      {errorMsg && <div className="text-red-500 mb-4">{errorMsg}</div>}
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title={tableTitle}
          searchTerm={searchTerm}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={() => setShowAddFloorForm(true)}
          columns={columns}
          data={filteredFloors}
          showSearch={true}
          showAddButton={true}
        />
      )}

      {showAddFloorForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <AddFloorForm onClose={() => setShowAddFloorForm(false)} refreshFloors={fetchFloors} facility={facility} />
            </div>
          {/* </div> */}
        </div>
      )}

      {showViewFloorForm && selectedFloor && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white p-1 shadow-lg rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewEditFloorForm
                facility={facility}
                floorData={selectedFloor}
                onClose={() => setShowViewFloorForm(false)}
                refreshFloors={fetchFloors}
              />
            </div>
          {/* </div> */}
        </div>
      )}

      <Tooltip id="floor-header-tooltip" place="top" effect="solid" />
    </div>
  );
};

export default Floor;
