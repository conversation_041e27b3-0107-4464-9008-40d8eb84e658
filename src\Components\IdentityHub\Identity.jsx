import React, { useState, useEffect, useCallback } from "react";
import EditableSection from "../Global/EditabelForIdentity";
import { getIdentityById , updateIdentity } from "../../api/identity";
import { useSearchParams } from "react-router-dom";
import { toast } from "react-toastify";
import { useIdentityData } from "../../hooks/useIdentityData";

const Demographic = () => {
  const [searchParams] = useSearchParams();
  const identityId = searchParams.get("identity_id");
   const {masterData, statusOptions, typeOptions } = useIdentityData();
  const [biographicData, setBiographicData] = useState({
    IdentityType: { label: "Identity Type", value: "" },
    SSN: { label: "SSN/National ID", value: "" },
    FirstName: { label: "First Name", value: "" },
    MiddleName: { label: "Middle Name", value: "" },
    LastName: { label: "Last Name", value: "" },
    Suffix: { label: "Suffix", value: "" },
    Email: { label: "Email", value: "" },
    Mobile: { label: "Mobile", value: "" },
    StartDate: { label: "Start Date", value: "" },
    EndDate: { label: "End Date", value: "" },
    Status: { label: "Status", value: "" },
  });

  const [personnelData, setPersonnelData] = useState({
    Suspension: { label: "Suspension", value: "" },
    SuspensionDate: { label: "Suspension Date", value: "" },
    Reason: { label: "Reason", value: "" },
  });

  const fetchIdentityData = useCallback(async () => {
    try {
      const {data} = await getIdentityById(identityId); 
      console.log(data)
      setBiographicData({
        IdentityType: { label: "Identity Type", value: data.identity_type },
        SSN: { label: "SSN/National ID", value: data.national_id },
        FirstName: { label: "First Name", value: data.first_name },
        MiddleName: { label: "Middle Name", value: data.middle_name },
        LastName: { label: "Last Name", value: data.last_name },
        Suffix: { label: "Suffix", value: data.suffix },
        Email: { label: "Email", value: data.email },
        Mobile: { label: "Mobile", value: data.mobile },
        StartDate: { label: "Start Date", value: data.start_date },
        EndDate: { label: "End Date", value: data.end_date },
        Status: { label: "Status", value: data.status },
      });

      setPersonnelData({
        Suspension: { label: "Suspension", value: data.suspension ? "Yes" : "No" },
        SuspensionDate: { label: "Suspension Date", value: data.suspension_date },
        Reason: { label: "Reason", value: data.reason },
      });
    } catch (error) {
      toast.error("Failed to fetch identity data.");
      console.error("Error fetching identity data:", error);
    }
  }, [identityId]);

  useEffect(() => {
    if (identityId) {
      fetchIdentityData(); // Ensure the API is called only once
    } else {
      toast.error("Missing identity ID in URL.");
    }
  }, [identityId, fetchIdentityData]);

  const handleInputChange = (section, key, value) => {
    if (section === "biographic") {
      setBiographicData((prev) => ({
        ...prev,
        [key]: { ...prev[key], value },
      }));
    } else if (section === "personnel") {
      setPersonnelData((prev) => ({
        ...prev,
        [key]: { ...prev[key], value },
      }));
    }
  };

  const formatDateForBackend = (dateStr) => {
  if (!dateStr) return null;
  const date = new Date(dateStr);
  return isNaN(date) ? null : date.toISOString().split("T")[0]; // format: YYYY-MM-DD
};

  const handleSave = async (section, updatedData) => {
  let identityPayload = {};

  if (section === "biographic") {
    identityPayload = {
      identity_type: Number(updatedData.IdentityType.value),
      national_id: String(updatedData.SSN.value || ""),
      first_name: String(updatedData.FirstName.value || ""),
      middle_name: String(updatedData.MiddleName.value || ""),
      last_name: String(updatedData.LastName.value || ""),
      suffix: String(updatedData.Suffix.value || ""),
      email: String(updatedData.Email.value || ""),
      mobile: String(updatedData.Mobile.value || ""),
      start_date: formatDateForBackend(updatedData.StartDate.value),
      end_date: formatDateForBackend(updatedData.EndDate.value),
      status: Number(updatedData.Status.value),
    };
  } else if (section === "personnel") {
    identityPayload = {
      suspension: updatedData.Suspension.value === "Yes",
      suspension_date: formatDateForBackend(updatedData.SuspensionDate.value),
      reason: String(updatedData.Reason.value || ""),
    };
  }

  try {
    await updateIdentity(identityId, identityPayload);
    toast.success("Identity updated successfully.");
    fetchIdentityData(); // Re-fetch to update UI
  } catch (error) {
    console.error("Update failed:", error);
    toast.error("Failed to update identity.");
  }
};

  const suspensionOptions = [
    { label: "Yes", value: "Yes" },
    { label: "No", value: "No" },
  ];

  return (
    <div className="bg-gray-100 min-h-screen">
      <EditableSection
        title="Biographic"
        data={biographicData}
        onChange={(key, value) => handleInputChange("biographic", key, value)}
        dropdownKeys={["IdentityType", "Status"]}
        dropdownOptions={{
          IdentityType: typeOptions,
          Status: statusOptions,
        }}
        onSave={(data) => handleSave("biographic", data)}
      />

      <EditableSection
        title="Personnel Actions"
        data={personnelData}
        onChange={(key, value) => handleInputChange("personnel", key, value)}
        dropdownKeys={["Suspension"]}
        dropdownOptions={{
          Suspension: suspensionOptions,  
        }}
        onSave={(data) => handleSave("personnel", data)}
      />
    </div>
  );
};

export default Demographic;
