import React, { useRef } from 'react';
import Webcam from 'react-webcam';
import { FaImages } from "react-icons/fa";
import { IoIosCamera } from "react-icons/io"; 
import Button from '../Button';
import demoimg from '../../../Images/demoimg.svg'; 
const ImageCapture = ({ onImageCaptured, onImageUploaded, error=null  }) => {
  const webcamRef = useRef(null);
  const [showCamera, setShowCamera] = React.useState(false);
  const [imageSrc, setImageSrc] = React.useState(null);

  const captureImage = () => {
    if (webcamRef.current) {
      const imageSrc = webcamRef.current.getScreenshot();
      if (imageSrc) {
        setImageSrc(imageSrc);
        onImageCaptured(imageSrc);
        setShowCamera(false);
      }
    }
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageSrc(reader.result);
        onImageUploaded(reader.result);
        setShowCamera(false);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <div className="relative flex flex-col items-center mb-4">
      {showCamera ? (
        <div className="w-32 h-32 md:w-48 md:h-48 relative">
          <Webcam
            audio={false}
            ref={webcamRef}
            screenshotFormat="image/jpeg"
            videoConstraints={{ facingMode: "user" }}
            className="rounded-full object-cover w-[150px] h-[150px] sm:w-[200px] sm:h-[200px] max-w-full"
          />
          <div className="flex gap-3 justify-center mt-2 mb-2">
            <Button
              type="secondary"
              onClick={() => document.getElementById('fileInput').click()}
              rounded
            label={<FaImages />}
            />
            
            <Button
              type="primary"
              onClick={captureImage}
              rounded
              label="Done"
            />
          </div>
          <input
            id="fileInput"
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleImageUpload}
          />
          {error && <p className="text-red-500">{error?.message}</p>}
        </div>
      ) : (
        <div className="flex flex-col items-center relative">
          <img
            src={imageSrc || demoimg}
            alt="Patient"
            className="w-24 h-24 md:w-32 md:h-32 rounded-full object-cover border border-gray-300"
          />
          <button
            type="button"
            className="absolute bottom-1 left-24 bg-[#EAE0F6] p-2 rounded-full  shadow-lg "
            onClick={() => setShowCamera(true)}
          >
            <IoIosCamera className="text-2xl  text-[#4F2683]" />
          </button>
          {error && <p className="text-red-500">{error?.message}</p>}
        </div>
      )}
    </div>
  );
};

export default ImageCapture;
