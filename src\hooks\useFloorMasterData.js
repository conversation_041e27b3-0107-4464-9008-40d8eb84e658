import { useState, useCallback, useEffect, useMemo } from "react";
import { getMasterData } from "../api/global";
import { toast } from "react-toastify";

// Module-level cache for floor master data
let floorMasterDataCache = null;

export const useFloorMasterData = () => {
  const [masterData, setMasterData] = useState(
    floorMasterDataCache || { floor_status: [], floor_occupancy_type: [] }
  );

  const fetchMasterData = useCallback(async () => {
    if (floorMasterDataCache) return;
    try {
      const res = await getMasterData({
        groups: ["floor_status", "floor_occupancy_type"],
      });
      floorMasterDataCache = res.data;
      setMasterData(res.data);
    } catch (error) {
      toast.error("Error fetching floor master data");
    }
  }, []);

  useEffect(() => {
    if (!floorMasterDataCache) {
      fetchMasterData();
    }
  }, [fetchMasterData]);

  const statusOptions = useMemo(() => {
    return masterData.floor_status.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.floor_status]);

  const occupancyOptions = useMemo(() => {
    return masterData.floor_occupancy_type.map((item) => ({
      label: item.value,
      value: Number(item.key),
    }));
  }, [masterData.floor_occupancy_type]);

  return { masterData, statusOptions, occupancyOptions };
};
