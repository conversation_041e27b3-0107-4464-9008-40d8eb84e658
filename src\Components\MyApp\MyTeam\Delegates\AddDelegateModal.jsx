import React, { useState } from "react";
import CustomDropdown from "../../../Global/CustomDropdown";
import DateTimeInput from "../../../Temporary/DateTimeInput";
import Input from "../../../Global/Input/Input";

const AddDelegateModal = ({ isOpen, onClose, onAdd }) => {
  const [formData, setFormData] = useState({
    delegate: "",
    taskType: "",
    sendNotificationTo: "",
    startDate: "",
    endDate: "",
    reason: "",
  });

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({ ...formData, [name]: value });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onAdd(formData);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="w-full max-w-3xl bg-white rounded-lg shadow-lg h-full overflow-y-auto">
        <div className="flex items-center mb-2 px-4 pt-2 justify-between">
          <h2 className="text-[30px] font-normal text-[#4F2683]">
            Add Delegation 
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={onClose}
          >
            &times;
          </button>
        </div>
        <hr className="mx-3" />
        <form onSubmit={handleSubmit} className="p-6 rounded-lg">
          <h2 className="text-[20px] text-[#333333] font-medium pb-4">
            Delegation Details 
          </h2>
          <div className="flex items-center mb-4">
            <label className="w-1/4 text-[16px] font-normal text-[#333333]">
              Delegate *
            </label>
            <div className="w-3/4">
              <input
                type="text"
                name="delegate"
                value={formData.delegate}
                onChange={handleChange}
                className="p-2 border h-11 rounded focus:outline-none focus:ring-1 w-full"
                required
              />
            </div>
          </div>
          <div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Task To Delegate *
  </label>
  <div className="w-3/4">
    <CustomDropdown
      options={[
        { label: "Task 1", value: "task1" },
        { label: "Task 2", value: "task2" },
        // ... add more options as needed
      ]}
      placeholder="Select a task"
      value={formData.taskType}
      onSelect={(selectedValue) =>
        setFormData({ ...formData, taskType: selectedValue })
      }
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Send Notification To
  </label>
  <div className="w-3/4">
    <CustomDropdown
      options={[
        { label: "User A", value: "userA" },
        { label: "User B", value: "userB" },
        // ... add more options as needed
      ]}
      placeholder="Select user"
      value={formData.sendNotificationTo}
      onSelect={(selectedValue) =>
        setFormData({ ...formData, sendNotificationTo: selectedValue })
      }
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    Start Date *
  </label>
  <div className="w-3/4">
    <DateTimeInput
      
      value={formData.startDate}
      onChange={(date) =>
        setFormData({ ...formData, startDate: date })
      }
      placeholder="Select start date & time"
    />
  </div>
</div>
<div className="flex items-center mb-4">
  <label className="w-1/4 text-[16px] font-normal text-[#333333]">
    End Date *
  </label>
  <div className="w-3/4">
    <DateTimeInput
      
      value={formData.endDate}
      onChange={(date) =>
        setFormData({ ...formData, endDate: date })
      }
      placeholder="Select end date & time"
    />
  </div>
</div>
<div className="flex mb-2 items-center">
                <label className="w-1/4">Reason *</label>
                <div className="w-3/4">
                  <Input
                    name="justification"
                    type="bubbles"
                    placeholder="Reason"
                    value={formData.reason}
                    height="94px"
                    bubbles={true}
                    bubbleOptions={[
                      "Lost Permanent Card",
                      "Forgot Permanent Card",
                    ]}
                    onChange={handleChange}
                  />
                </div>
              </div>
          <div className="flex justify-center gap-4 mt-6">
            <button
              type="button"
              className="bg-gray-300 text-gray-700 px-4 py-2 rounded"
              onClick={onClose}
            >
              Cancel
            </button>
            <button
              type="submit"
              className="bg-[#4F2683] text-white px-4 py-2 rounded"
            >
              Save
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddDelegateModal;
