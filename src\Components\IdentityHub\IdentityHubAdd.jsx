import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { IoIosArrowBack } from "react-icons/io";
import ImageCapture from "../../Components/Global/ImageAndCamera/ImageCaptureForForm";
import Input from "../Global/Input/Input";
import DateInput from "../Global/Input/DateInput";
import CustomDropdown from "../../Components/Global/CustomDropdown";
import Button from "../../Components/Global/Button";
import { useIdentityData } from "../../hooks/useIdentityData";
import { createIdentity } from "../../api/identity";

const IdentityHubAdd = () => {
 const {masterData, identityStatusOptions, typeOptions } = useIdentityData();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    identityType: "",
    ssnNationalId: "",
    firstName: "",
    middleName: "",
    lastName: "",
    suffix: "",
    email: "",
    mobile: "",
    startDate: "",
    endDate: "",
    status: "",
    suspension: "",
    suspensionDate: "",
    reason: "",
  });
  const [image, setImage] = useState(null);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const validateForm = () => {
    // Validate required fields and show specific field names in toast
    const requiredFields = [];

    if (!formData.firstName) {
      requiredFields.push("First Name");
    }
    if (!formData.lastName) {
      requiredFields.push("Last Name");
    }
    if (!formData.identityType || formData.identityType === "Select Identity Type") {
      requiredFields.push("Identity Type");
    }
    if (!formData.status || formData.status === "Select Status") {
      requiredFields.push("Status");
    }

    if (requiredFields.length > 0) {
      const fieldNames = requiredFields.join(", ");
      toast.error(`Please fill the following required field(s): ${fieldNames}`);
      return false;
    }

    return true;
  };

  const handleSave = async (e) => {
    e.preventDefault();
    if (!validateForm()) return;

    const newEntry = {
      identity_type: formData.identityType,
      national_id: formData.ssnNationalId,
      first_name: formData.firstName,
      middle_name: formData.middleName,
      last_name: formData.lastName,
      suffix: formData.suffix,
      email: formData.email,
      mobile: formData.mobile,
      start_date: formData.startDate,
      end_date: formData.endDate,
      status: formData.status,
      suspension: formData.suspension === "Yes", // Convert to boolean
      suspension_date: formData.suspensionDate,
      reason: formData.reason,
      // image, // include image data if needed
    };

    try {
      await createIdentity(newEntry); // Ensure the API call is awaited
      toast.success("Identity added successfully! Redirecting...", {
        autoClose: 2000,
        pauseOnHover: false,
      });

      setTimeout(() => {
        navigate("/identity-hub", { state: { newIdentity: newEntry } });
      }, 2000);
    } catch (error) {
      toast.error("Failed to add identity. Please try again.");
      console.error("Error adding identity:", error);
    }
  };



  return (
    <>
      <div className="flex items-center gap-2 pl-24 pt-20 text-[#4F2683]">
        <div
          className="flex items-center gap-1 cursor-pointer"
          onClick={() => navigate("/identity-hub")}
        >
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Add Identity</h2>
        </div>
      </div>

      <div className="p-6 pl-20">
        <form className="space-y-4" onSubmit={handleSave}>
          <div className="w-full bg-white shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] rounded-[10px] p-4 border">
            {/* Image Capture Section */}
            <div className="pb-12">
              <ImageCapture
                onImageCaptured={setImage}
                onImageUploaded={setImage}
              />
            </div>

            {/* Identity Type * */}
            <h2 className="font-poppins">Biographic Data</h2>
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Identity Type *</label>
              <CustomDropdown
                options={typeOptions} // Use typeOptions from the hook
                placeholder="Select Identity Type"
                value={formData.identityType}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, identityType: value }))
                }

              />
            </div>

            {/* SSN/National ID * */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">SSN/National ID *</label>
              <Input
                type="text"
                name="ssnNationalId"
                value={formData.ssnNationalId}
                onChange={handleInputChange}
                placeholder="Enter SSN or National ID"
              />
            </div>

            {/* First Name */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">First Name *</label>
              <Input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleInputChange}
                placeholder="Enter First Name"
              />
            </div>

            {/* Middle Name */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Middle Name</label>
              <Input
                type="text"
                name="middleName"
                value={formData.middleName}
                onChange={handleInputChange}
                placeholder="Enter Middle Name"
              />
            </div>

            {/* Last Name */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Last Name *</label>
              <Input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleInputChange}
                placeholder="Enter Last Name"
              />
            </div>

            {/* Suffix */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Suffix</label>
              <Input
                type="text"
                name="suffix"
                value={formData.suffix}
                onChange={handleInputChange}
                placeholder="Enter Suffix"
              />
            </div>

            {/* Email */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Email</label>
              <Input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter Email"
              />
            </div>

            {/* Mobile */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Mobile</label>
              <Input
                type="text"
                name="mobile"
                value={formData.mobile}
                onChange={handleInputChange}
                placeholder="Enter Mobile"
              />
            </div>

            {/* Start Date */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Start Date</label>
              <DateInput
                name="startDate"
                value={formData.startDate}
                onChange={(date) =>
                  setFormData((prev) => ({ ...prev, startDate: date }))
                }
                placeholder="MM-DD-YYYY"
                className="w-full"
              />
            </div>

            {/* End Date */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">End Date</label>
              <DateInput
                name="endDate"
                value={formData.endDate}
                onChange={(date) =>
                  setFormData((prev) => ({ ...prev, endDate: date }))
                }
                placeholder="MM-DD-YYYY"
                className="w-full"
              />
            </div>

            {/* Status */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Status *</label>
              <CustomDropdown
                options={identityStatusOptions}
                placeholder="Select Status"
                defaultValue="Active"
                value={formData.status}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, status: value }))
                }
              />
            </div>
<h2>Biographic Data</h2>
            {/* Suspension */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Suspension</label>
              <CustomDropdown
                options={["Yes", "No"]}
                placeholder="Select Suspension"
                defaultValue="No"
                value={formData.suspension}
                rounded="rounded-md"
                bgColor="bg-white"
                textColor="text-gray-700"
                hoverBgColor="hover:bg-[#4F2683]"
                borderColor="border-gray-300"
                className="w-full h-11"
                onSelect={(value) =>
                  setFormData((prev) => ({ ...prev, suspension: value }))
                }
              />
            </div>

            {/* Suspension Date */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Suspension Date</label>
              <DateInput
                name="suspensionDate"
                value={formData.suspensionDate}
                onChange={(date) =>
                  setFormData((prev) => ({ ...prev, suspensionDate: date }))
                }
                placeholder="MM-DD-YYYY"
                className="w-full"
              />
            </div>

            {/* Reason */}
            <div className="flex items-center gap-4 py-2">
              <label className="w-1/3">Reason</label>
              <textarea
                name="reason"
                value={formData.reason}
                onChange={handleInputChange}
                placeholder="Enter Reason"
                className="border rounded-md w-full p-2 focus:ring-1 outline-none focus:ring-[#4F2683] h-20"
              />
            </div>
          </div>

          <div className="flex justify-center gap-1 mt-6">
            <Button
              type="cancel"
              buttonType="button"
              label="Cancel"
              onClick={() => navigate("/identity-hub")}
            />
            <Button type="primary" buttonType="submit" label="Save" />
          </div>
        </form>
      </div>
    </>
  );
};

export default IdentityHubAdd;
