import React, { useState, useEffect } from "react";
import { createDeviceInFacility, getDevicesForFacility } from "../../api/Device";
import { getKioskGroups } from "../../api/global";
import Button from "../Global/Button";
import CustomDropdown from "../Global/CustomDropdown";
import { useSelector } from "react-redux";
import { useBuildingData } from "../../hooks/useBuildingData";
import { useFloorData } from "../../hooks/useFloorData";
import { useRoomData } from "../../hooks/useRoomData";

const AddDeviceModal = ({ open, onClose, onSave, newEntry, setNewEntry }) => {
  const [show, setShow] = useState(false);

  // Get current facility from Redux store
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const selectedFacilityName = useSelector(state => state.facility.selectedFacilityName);

  // Building/Floor/Room selection state
  const [selectedBuilding, setSelectedBuilding] = useState('');
  const [selectedFloor, setSelectedFloor] = useState('');
  const [devices, setDevices] = useState([]);
  const [kioskGroups, setKioskGroups] = useState([]);
  const buildingOptions = useBuildingData(selectedFacilityId);
  const floorOptions = useFloorData(selectedBuilding);
  const roomOptions = useRoomData(selectedFloor);

  useEffect(() => {
    if (open) {
      setTimeout(() => setShow(true), 10);
      if (selectedFacilityId) {
        getDevicesForFacility(selectedFacilityId)
          .then((res) => setDevices(res))
          .catch(() => setDevices([]));
      }
      // Fetch kiosk groups when modal opens
      getKioskGroups()
        .then((res) => {
          console.log('Kiosk Groups Response:', res);
          if (res?.status && Array.isArray(res?.data?.data)) {
            setKioskGroups(res.data.data);
          } else {
            setKioskGroups([]);
          }
        })
        .catch((err) => {
          console.error('Error fetching kiosk groups:', err);
          setKioskGroups([]);
        });
    } else {
      setShow(false);
    }
  }, [open, selectedFacilityId]);

  if (!open) return null;

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    // Only send allowed fields to API
    const updatedEntry = {
      name: newEntry.device,
      identifier: newEntry.identifier,
      kiosk_group_id: newEntry.deviceGroup
    };
    setNewEntry({ ...newEntry });
    try {
      await createDeviceInFacility(selectedFacilityId, updatedEntry);
      const refreshed = await getDevicesForFacility(selectedFacilityId);
      setDevices(refreshed);
      onSave();
    } catch (err) {
      // Handle error (toast, etc.)
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-white p-6 rounded shadow-lg w-full max-w-3xl h-full overflow-y-auto transform transition-transform duration-700 ease-in-out ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-[30px] font-normal text-[#4F2683]">Add Device</h3>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={handleClose}
          >
            &times;
          </button>
        </div>
        <hr className="mb-4" />
        <form className="bg-white p-2 rounded-lg" onSubmit={handleFormSubmit}>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Name*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Device Name"
                value={newEntry.device}
                onChange={e => setNewEntry({ ...newEntry, device: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Identifier*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded"
                placeholder="Identifier"
                value={newEntry.identifier || ''}
                onChange={e => setNewEntry({ ...newEntry, identifier: e.target.value })}
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Device Group*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={kioskGroups.map(group => ({ 
                  value: group.kiosk_group_id, 
                  label: group.name 
                }))}
                value={newEntry.deviceGroup}
                onSelect={value => setNewEntry({ ...newEntry, deviceGroup: value })}
                placeholder="Select Device Group"
                className="w-full h-10"
                required
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Facility*
            </label>
            <div className="w-3/4">
              <input
                className="border p-2 w-full rounded bg-gray-100"
                value={selectedFacilityName || 'No facility selected'}
                readOnly
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Building*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={buildingOptions}
                value={selectedBuilding}
                onSelect={(value) => {
                  setSelectedBuilding(value);
                  setSelectedFloor('');
                  setNewEntry({ ...newEntry, building: value, floor: '', room: '' });
                }}
                placeholder="Select Building"
                className="w-full h-10"
                disabled={!selectedFacilityId}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Floor*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={floorOptions}
                value={selectedFloor}
                onSelect={(value) => {
                  setSelectedFloor(value);
                  setNewEntry({ ...newEntry, floor: value, room: '' });
                }}
                placeholder="Select Floor"
                className="w-full h-10"
                disabled={!selectedBuilding}
              />
            </div>
          </div>
          <div className="flex mb-4">
            <label className="w-1/4 flex items-center text-[16px] font-normal text-[#333333]">
              Room*
            </label>
            <div className="w-3/4">
              <CustomDropdown
                options={roomOptions}
                value={newEntry.room}
                onSelect={(value) => {
                  setNewEntry({ ...newEntry, room: value });
                }}
                placeholder="Select Room"
                className="w-full h-10"
                disabled={!selectedFloor}
              />
            </div>
          </div>
          <div className="flex justify-center gap-4">
            <Button
              type="cancel"
              onClick={handleClose}
              label="Cancel"
            />
            <Button
              type="primary"
              label="Save"
            />
          </div>
        </form>
        {/* <div className="mt-6">
          <h4 className="text-lg font-semibold mb-2">All Devices</h4>
          <ul className="list-disc pl-6">
            {devices.map((device) => (
              <li key={device.id}>{device.device} ({device.deviceGroup})</li>
            ))}
          </ul>
        </div> */}
      </div>
    </div>
  );
};

export default AddDeviceModal;
