import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import { Box, CircularProgress, Typography } from '@mui/material';
import DesignerContainer from '../../Components/IDDesigner/DesignerContainer';
import { loadTemplate, markTemplateAsSaved, setLoading } from '../../redux/idDesignerSlice';
import { getBadgeTemplateById } from '../../api/badge';
import {
  loadTemplateFromStorage,
  saveTemplateToStorage,
  isTemplateLocallyModified,
  markTemplateAsSynced
} from '../../utils/templateStorage';
import imageManager from '../../utils/imageManager';
import { migrateElementImages, needsImageMigration } from '../../utils/imageMigration';

const IDDesigner = () => {
  const { templateId } = useParams();
  const dispatch = useDispatch();
  const { loading, templateChanged, currentTemplate } = useSelector((state) => state.idDesigner);
  const [initialLoading, setInitialLoading] = useState(true);
  const [templateMeta, setTemplateMeta] = useState({});

  useEffect(() => {
    const loadTemplateData = async () => {
      if (templateId) {
        try {
          setInitialLoading(true);
          dispatch(setLoading(true));

          // First check if we have local data
          const localData = loadTemplateFromStorage(templateId);

          // Fetch from API
          const response = await getBadgeTemplateById(templateId);
          const apiData = response.data;

          let templateToLoad = null;

          setTemplateMeta({
              id: apiData.badge_id,
              name: apiData.name,
              schema: apiData.schema,
              key: apiData.key,
              format: apiData.format
            });

          // Check if local data is more recent or if we should use API data
          if (localData && isTemplateLocallyModified(templateId, apiData.updated_at)) {
            // Use local data if it's more recent
            let elements = localData.content?.elements || localData.config?.elements || localData.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: templateId,
              lastModified: localData.lastModified,
              canvasConfig: localData.content?.canvasConfig || localData.config?.canvasConfig || localData.canvasConfig || {},
              elements: elements,
            };
            toast.info(`Loaded local version of "${localData.name}" (has unsaved changes)`);
          } else {
            // Use API data and save to local storage
            let elements = apiData.content?.elements || apiData.config?.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            templateToLoad = {
              id: apiData.badge_id,
              lastModified: apiData.updated_at || apiData.created_at,
              canvasConfig: apiData.content?.canvasConfig || apiData.config?.canvasConfig || {},
              elements: elements,
            };

            // Save API data to local storage
            saveTemplateToStorage(templateId, {
              content: {
                canvasConfig: templateToLoad.canvasConfig,
                elements: templateToLoad.elements
              },
            });

            // Mark as synced since we just loaded from API
            markTemplateAsSynced(templateId);

            toast.success(`Template "${templateToLoad.name}" loaded successfully`);
          }

          // Load the template into the designer
          dispatch(loadTemplate(templateToLoad));

          // Mark as saved if using API data
          if (!localData || !isTemplateLocallyModified(templateId, apiData.updated_at)) {
            dispatch(markTemplateAsSaved());
          }

        } catch (error) {
          console.error('Error loading template:', error);

          // Try to load from local storage as fallback
          const localData = loadTemplateFromStorage(templateId);
          if (localData) {
            let elements = localData.content?.elements || localData.config?.elements || localData.elements || [];

            // Migrate base64 images if needed
            if (needsImageMigration(elements)) {
              elements = migrateElementImages(elements);
              toast.info('Migrating images to new format...');
            }

            dispatch(loadTemplate({
              id: templateId,
              lastModified: localData.lastModified,
              canvasConfig: localData.content?.canvasConfig || localData.config?.canvasConfig || localData.canvasConfig || {},
              elements: elements,
            }));
            toast.warning(`Loaded local version of "${localData.name}" (API unavailable)`);
          } else {
            toast.error('Failed to load template');
          }
        } finally {
          setInitialLoading(false);
          dispatch(setLoading(false));
        }
      }
    };

    loadTemplateData();
  }, [templateId, dispatch]);

  // Cleanup image manager when component unmounts
  useEffect(() => {
    return () => {
      imageManager.clear();
    };
  }, []);

  if (initialLoading) {
    return (
      <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            gap: 2
          }}
        >
          <CircularProgress size={60} sx={{ color: '#4F2683' }} />
          <Typography variant="h6" color="#4F2683">
            Loading Template...
          </Typography>
        </Box>
      </div>
    );
  }

  return (
    <div style={{ height: 'calc(100vh - 3.5rem)' }} className="flex flex-col px-8 py-4 pl-20 absolute mt-14 w-full">
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
        <h2 className="font-normal text-[24px] text-[#4F2683]">
          ID Card Designer{currentTemplate.name ? ` - ${currentTemplate.name}` : ''}
        </h2>
        {templateChanged && (
          <Typography
            variant="caption"
            sx={{
              backgroundColor: '#ff9800',
              color: 'white',
              px: 1,
              py: 0.5,
              borderRadius: 1,
              fontWeight: 'bold'
            }}
          >
            UNSAVED
          </Typography>
        )}
      </Box>
      <div className="flex-1 overflow-hidden">
        <DesignerContainer templateMeta={templateMeta} />
      </div>
    </div>
  );
};

export default IDDesigner;
