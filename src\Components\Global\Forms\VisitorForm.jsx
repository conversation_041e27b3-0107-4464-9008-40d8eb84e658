import React, { useRef, useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Button from "../Button";
import ImageCapture from "../ImageAndCamera/ImageCaptureForForm";
import Input from "../Input/Input";
import DateInput from "../Input/DateInput";
import CustomDropdown from "../CustomDropdown";
import demo from "../../../Images/fromimg.svg";
import { createVisitWithGuest } from '../../../api/visitor-hub';
import { useFacilityOptions } from '../../../hooks/useFacilityOptions';
import { setSelectedFacility } from '../../../redux/facilitySlice';
import { searchIdentities, getIdentities } from '../../../api/global';

const VisitorForm = ({ fieldsToRender, onAddGuest, onClose, hostName, hostId, selectedHost, escortId, selectedEscort }) => {
  const defaultFields = [
    "facility",
    "escortName",
    "hostName",
    "startDate",
    "startTime",
    "endTime",
    "visitorName",
    "dob",
    "guestMail",
    "phoneNumber",
    "relationship",
  ];
  fieldsToRender = fieldsToRender || defaultFields;

  const submitActionRef = useRef("");
  const dispatch = useDispatch();
  const facilityOptions = useFacilityOptions();
  const selectedFacilityName = useSelector(state => state.facility.selectedFacilityName);
  const selectedFacilityId = useSelector(state => state.facility.selectedFacilityId);
  const durationOptions = ["2:00 Hour", "4:00 Hour", "8:00 Hour", "12:00 Hour"];
  
  // State for host and escort dropdown options
  const [hostOptions, setHostOptions] = useState([]);
  const [escortOptions, setEscortOptions] = useState([]);
  const [isLoadingHosts, setIsLoadingHosts] = useState(false);
  const [isLoadingEscorts, setIsLoadingEscorts] = useState(false);
  const [hostSearchTerm, setHostSearchTerm] = useState("");
  const [escortSearchTerm, setEscortSearchTerm] = useState("");
  const [showHostDropdown, setShowHostDropdown] = useState(false);
  const [showEscortDropdown, setShowEscortDropdown] = useState(false);
  
  // Fetch all identities from master data
  const fetchAllIdentities = async () => {
    setIsLoadingHosts(true);
    setIsLoadingEscorts(true);
    try {
      const response = await getIdentities();
      const identities = response?.data?.data || [];
      const formattedIdentities = identities.map(identity => ({
        value: identity.id,
        label: identity.name || `${identity.first_name || ''} ${identity.last_name || ''}`.trim(),
        data: identity
      }));
      
      setHostOptions(formattedIdentities);
      setEscortOptions(formattedIdentities);
    } catch (error) {
      console.error("Error fetching identities:", error);
      setHostOptions([]);
      setEscortOptions([]);
    } finally {
      setIsLoadingHosts(false);
      setIsLoadingEscorts(false);
    }
  };
  
  // Fetch hosts from master data with search
  const fetchHosts = async (searchTerm = "") => {
    if (!searchTerm.trim()) {
      return;
    }
    
    setIsLoadingHosts(true);
    try {
      const response = await searchIdentities(searchTerm);
      const hosts = response?.data?.data || [];
      const formattedHosts = hosts.map(host => ({
        value: host.id,
        label: host.name || `${host.first_name || ''} ${host.last_name || ''}`.trim(),
        data: host
      }));
      setHostOptions(formattedHosts);
    } catch (error) {
      console.error("Error fetching hosts:", error);
      setHostOptions([]);
    } finally {
      setIsLoadingHosts(false);
    }
  };
  
  // Fetch escorts from master data with search
  const fetchEscorts = async (searchTerm = "") => {
    if (!searchTerm.trim()) {
      return;
    }
    
    setIsLoadingEscorts(true);
    try {
      const response = await searchIdentities(searchTerm);
      const escorts = response?.data?.data || [];
      const formattedEscorts = escorts.map(escort => ({
        value: escort.id,
        label: escort.name || `${escort.first_name || ''} ${escort.last_name || ''}`.trim(),
        data: escort
      }));
      setEscortOptions(formattedEscorts);
    } catch (error) {
      console.error("Error fetching escorts:", error);
      setEscortOptions([]);
    } finally {
      setIsLoadingEscorts(false);
    }
  };
  
  // Load all identities when component mounts
  useEffect(() => {
    fetchAllIdentities();
  }, []);
  
  // Effect to handle host search term changes
  useEffect(() => {
    const delaySearch = setTimeout(() => {
      if (hostSearchTerm) {
        fetchHosts(hostSearchTerm);
        setShowHostDropdown(true);
      }
    }, 500); // Debounce search
    
    return () => clearTimeout(delaySearch);
  }, [hostSearchTerm]);
  
  // Effect to handle escort search term changes
  useEffect(() => {
    const delaySearch = setTimeout(() => {
      if (escortSearchTerm) {
        fetchEscorts(escortSearchTerm);
        setShowEscortDropdown(true);
      }
    }, 500); // Debounce search
    
    return () => clearTimeout(delaySearch);
  }, [escortSearchTerm]);
  
  // Click outside handlers
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (!event.target.closest('.host-dropdown-container')) {
        setShowHostDropdown(false);
      }
      if (!event.target.closest('.escort-dropdown-container')) {
        setShowEscortDropdown(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Set startTime in 24-hour format
  const currentTime = new Date().toLocaleTimeString([], {
    hour12: false,
    hour: "2-digit",
    minute: "2-digit",
  });

  const initialValues = {
    // Patient Information
    facility: selectedFacilityId || "",
    hostName: hostName || "",
    hostId: hostId || "",
    escortName: "",
    escortId: "",
    startDate: new Date(),
    startTime: currentTime,
    endTime: "4:00 Hour", // Default duration is set here
    // Guest Information
    visitorName: "",
    dob: "",
    guestMail: "",
    phoneNumber: "",
    relationship: "",
    image: "",
  };

  const validationSchema = Yup.object({
    visitorName: Yup.string().required("Guest Name is required"),
  });

  const handleSubmit = async (values, { resetForm, setSubmitting }) => {
    console.log("Form submission started with values:", values);
    console.log("Selected facility from Redux:", selectedFacilityId, selectedFacilityName);
    console.log("Host info:", { hostName, hostId, selectedHost });
    console.log("Escort info:", { escortId, selectedEscort });

    const durationMap = {
      "2:00 Hour": 2,
      "4:00 Hour": 4,
      "8:00 Hour": 8,
      "12:00 Hour": 12,
    };
    const durationHours = durationMap[values.endTime] || 0;

    // Ensure startDate is in ISO date format (YYYY-MM-DD)
    const dateStr =
      typeof values.startDate === "string"
        ? values.startDate
        : values.startDate.toISOString().split("T")[0];

    // Combine date and time to form a valid Date object
    const startDateTime = new Date(`${dateStr}T${values.startTime}:00`);

    // Get facility ID with fallback
    const facilityId = selectedFacilityId || values.facility || localStorage.getItem("selectedFacility");
    const hostIdValue = values.hostId || hostId || selectedHost?.id || null;
    const escortIdValue = values.escortId || escortId || selectedEscort?.id || null;

    // Create API payload according to error message requirements
    const apiPayload = {
      facility_id: facilityId,
      host_id: hostIdValue,
      start_date: dateStr,
      start_time: values.startTime,
      duration: durationHours,
      guests: [
        {
          first_name: values.visitorName.split(' ')[0] || values.visitorName,
          last_name: values.visitorName.split(' ').slice(1).join(' ') || '',
          email: values.guestMail,
          mobile_phone: values.phoneNumber,
          date_of_birth: values.dob,
          // relationship: values.relationship,
          image: null
        }
      ]
    };
    
    // Add escort_id only if it exists
    if (escortIdValue) {
      apiPayload.escort_id = escortIdValue;
    }

    // Keep the local newGuest object for UI updates
    const newGuest = {
      visitorName: values.visitorName,
      dob: values.dob,
      guestMail: values.guestMail,
      phoneNumber: values.phoneNumber,
      relationship: values.relationship,
      screening: true,
      startDate: `${startDateTime.toLocaleDateString()} ${startDateTime.toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`,
      endDate: `${new Date(startDateTime.getTime() + durationHours * 60 * 60 * 1000).toLocaleDateString()} ${new Date(startDateTime.getTime() + durationHours * 60 * 60 * 1000).toLocaleTimeString([], {
        hour: "2-digit",
        minute: "2-digit",
      })}`,
      image: values.image || demo,
      facility: selectedFacilityId,
      facilityName: selectedFacilityName,
      hostName: values.hostName,
      hostId: values.hostId,
      escortName: values.escortName,
      escortId: values.escortId,
    };

    try {
      console.log("Submitting API payload:", apiPayload);
      console.log("Final facility ID used:", facilityId);
      console.log("Host ID:", hostId);
      console.log("Selected host:", selectedHost);

      // Check authentication
      const tokens = localStorage.getItem("tokens");
      if (!tokens) {
        throw new Error("Authentication required. Please log in first.");
      }

      // Validate required fields before API call
      if (!facilityId) {
        throw new Error("Facility ID is required. Please select a facility.");
      }

      if (!apiPayload.host_id) {
        throw new Error("Host ID is required. Please select a host first.");
      }

      if (!values.visitorName.trim()) {
        throw new Error("Visitor name is required.");
      }

      // Use the API payload for the actual API call
      const response = await createVisitWithGuest(apiPayload);
      console.log("Visit created successfully:", response);

      // Update the local newGuest with response data if available
      if (response?.data) {
        newGuest.id = response.data.visit?.id;
        newGuest.guest_id = response.data.guest?.id;
        newGuest.visit_id = response.data.visit?.id;
      }

      onAddGuest(newGuest);
      alert("Visit created successfully!");
    } catch (error) {
      // Optionally show error to user
      console.error("Error creating visit with guest:", error);
      console.error("Error details:", error.message);
      alert(`Failed to create visit: ${error.message}`);
    }

    if (submitActionRef.current === "addMore") {
      resetForm({
        values: {
          ...values,
          visitorName: "",
          dob: "",
          guestMail: "",
          phoneNumber: "",
          relationship: "",
          image: "",
        },
        errors: {},
        touched: {},
      });
    } else if (submitActionRef.current === "save") {
      resetForm();
      onClose();
    }
    setSubmitting(false);
  };

  const patientFields = ["facility", "hostName", "startDate", "startTime", "endTime"];
  const hasPatientFields = fieldsToRender.some((field) => patientFields.includes(field));

  return (
    <div className="px-12 py-6 mb-8 border rounded-md shadow-[0px_3.941415548324585px_7.88283109664917px_4.926765441894531px_rgba(79,38,131,0.06)]">
      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        validateOnMount={false}
      >
        {({ setFieldValue, values, submitForm, touched, errors, handleBlur }) => (
          <Form className="space-y-4">
            {/* Patient Information */}
            {hasPatientFields && (
              <>
                <div className="flex justify-between">
                  <h1 className="text-xl md:text-lg font-normal text-[#4F2683]">
                    Create Walk In Visit
                  </h1>
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                </div>
                <div className="bg-gray-100 p-4 py-6 rounded-lg border rounded-tr-3xl border-[#4F2683]">
                  <div className="flex gap-4 w-full">
                    {fieldsToRender.includes("facility") && (
                      <div className="w-1/4">
                        <h2>Facility</h2>
                        <div className="border h-11 p-2 flex items-center rounded-[6px] bg-[white] text-black">
                          {selectedFacilityName}
                        </div>
                      </div>
                    )}
                    {fieldsToRender.includes("hostName") && (
                      <div className="w-1/4 host-dropdown-container">
                        <h2>Host Name*</h2>
                        <div className="relative">
                          <Input
                            type="text"
                            name="hostName"
                            value={values.hostName}
                            className="border h-11 p-2 focus:outline-none rounded-[6px] focus:ring-1"
                            placeholder="Search Host Name"
                            onChange={(e) => {
                              setFieldValue("hostName", e.target.value);
                              setHostSearchTerm(e.target.value);
                            }}
                            onBlur={handleBlur}
                            disabled={!!hostName}
                          />
                          {showHostDropdown && hostOptions.length > 0 && !hostName && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                              {isLoadingHosts ? (
                                <div className="px-4 py-2 text-gray-500">Loading...</div>
                              ) : (
                                hostOptions.map((option) => (
                                  <div
                                    key={option.value}
                                    className="px-4 py-2 cursor-pointer hover:bg-[#4F2683] hover:text-white"
                                    onClick={() => {
                                      setFieldValue("hostName", option.label);
                                      setFieldValue("hostId", option.value);
                                      setShowHostDropdown(false);
                                      setHostSearchTerm("");
                                    }}
                                  >
                                    {option.label}
                                  </div>
                                ))
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    {fieldsToRender.includes("escortName") && (
                      <div className="w-1/4 escort-dropdown-container">
                        <h2>Escort Name</h2>
                        <div className="relative">
                          <Input
                            type="text"
                            name="escortName"
                            value={values.escortName}
                            className="border h-11 p-2 focus:outline-none rounded-[6px] focus:ring-1"
                            placeholder="Search Escort Name"
                            onChange={(e) => {
                              setFieldValue("escortName", e.target.value);
                              setEscortSearchTerm(e.target.value);
                            }}
                            onBlur={handleBlur}
                          />
                          {showEscortDropdown && escortOptions.length > 0 && (
                            <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                              {isLoadingEscorts ? (
                                <div className="px-4 py-2 text-gray-500">Loading...</div>
                              ) : (
                                escortOptions.map((option) => (
                                  <div
                                    key={option.value}
                                    className="px-4 py-2 cursor-pointer hover:bg-[#4F2683] hover:text-white"
                                    onClick={() => {
                                      setFieldValue("escortName", option.label);
                                      setFieldValue("escortId", option.value);
                                      setShowEscortDropdown(false);
                                      setEscortSearchTerm("");
                                    }}
                                  >
                                    {option.label}
                                  </div>
                                ))
                              )}
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                    {fieldsToRender.includes("startDate") && (
                      <div className="w-1/4">
                        <DateInput
                          label="Start Date"
                          name="startDate"
                          value={values.startDate}
                          onChange={(date) => setFieldValue("startDate", date)}
                          placeholder="MM-DD-YYYY"
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("startTime") && (
                      <div className="w-1/4">
                        <Input
                          type="time"
                          label="Start Time"
                          name="startTime"
                          value={values.startTime}
                          onChange={(e) => setFieldValue("startTime", e.target.value)}
                          onBlur={handleBlur}
                        />
                      </div>
                    )}
                    {fieldsToRender.includes("endTime") && (
                      <div className="w-1/4">
                        <h2>Duration</h2>
                        <CustomDropdown
                          options={durationOptions}
                          defaultValue="4:00 Hour"
                          bgColor="bg-[white] text-black"
                          textColor="text-black"
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                          rounded="rounded-[6px]"
                          className="border h-11 p-2 focus:outline-none focus:ring-1"
                          onChange={(value) => {
                            console.log("Selected Duration:", value);
                            setFieldValue("endTime", value);
                          }}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}

            {/* Guest Information */}
            <div className="p-4">
              <div className="flex justify-between mb-4">
                {!hasPatientFields && (
                  <button
                    type="button"
                    className="text-xl text-white rounded-full h-8 w-8 bg-[#4F2683]"
                    onClick={onClose}
                  >
                    &times;
                  </button>
                )}
              </div>
              <div>
                <ImageCapture
                  onImageCaptured={(img) => setFieldValue("image", img)}
                  onImageUploaded={(img) => setFieldValue("image", img)}
                />
              </div>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-16">
                {fieldsToRender.includes("visitorName") && (
                  <div className="w-full">
                    <Input
                      type="text"
                      label="Guest Name*"
                      name="visitorName"
                      value={values.visitorName}
                      placeholder="Enter Guest Name"
                      onChange={(e) => setFieldValue("visitorName", e.target.value)}
                      onBlur={handleBlur}
                    />
                    {touched.visitorName && errors.visitorName && (
                      <div className="text-red-500 text-sm">{errors.visitorName}</div>
                    )}
                  </div>
                )}
                {fieldsToRender.includes("dob") && (
                  <div className="w-full">
                    <DateInput
                      label="Guest DOB"
                      name="dob"
                      value={values.dob}
                      className="h-11"
                      onChange={(date) => setFieldValue("dob", date)}
                      placeholder="Select a date"
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("guestMail") && (
                  <div className="w-full">
                    <Input
                      type="email"
                      label="Guest Mail"
                      name="guestMail"
                      value={values.guestMail}
                      placeholder="Enter Guest Email"
                      onChange={(e) => setFieldValue("guestMail", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
                {fieldsToRender.includes("phoneNumber") && (
                  <div className="w-full">
                    <Input
                      type="tel"
                      label="Phone Number"
                      name="phoneNumber"
                      value={values.phoneNumber}
                      placeholder="Enter Phone Number"
                      onChange={(e) => setFieldValue("phoneNumber", e.target.value)}
                      onBlur={handleBlur}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Submission Buttons */}
            <div className="flex gap-4 pl-4 justify-center pb-4">
              <Button
                buttonType="button"
                type="primary"
                onClick={() => {
                  submitActionRef.current = "addMore";
                  submitForm();
                  console.log("Add More");
                }}
                label="Add More"
              />
              <Button
                type="primary"
                onClick={() => {
                  submitActionRef.current = "save";
                  submitForm();
                }}
                label="Save"
              />
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default VisitorForm;