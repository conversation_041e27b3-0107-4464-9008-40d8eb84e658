import React, { useState } from "react";
import Button from "../Global/Button";

function ApproversAdd({ onSubmit, onClose, availableApprovers }) {
  // Default sample approvers (8 entries)
  const defaultApprovers = [
    {
      name: "<PERSON>",
      eid: "A101",
      approverLevel: "Level 1",
      type: "Manager",
      organization: "Org1",
      jobTitle: "Supervisor",
      startDate: "2023-01-01",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "A102",
      approverLevel: "Level 2",
      type: "Senior Manager",
      organization: "Org2",
      jobTitle: "Team Lead",
      startDate: "2023-02-01",
      endDate: "2023-11-30",
      status: "Inactive",
    },
    {
      name: "<PERSON>",
      eid: "A103",
      approverLevel: "Level 1",
      type: "Manager",
      organization: "Org3",
      jobTitle: "Coordinator",
      startDate: "2023-03-01",
      endDate: "2023-10-31",
      status: "Active",
    },
    {
      name: "<PERSON>",
      eid: "A104",
      approverLevel: "Level 3",
      type: "Director",
      organization: "Org1",
      jobTitle: "Director",
      startDate: "2023-04-01",
      endDate: "2023-09-30",
      status: "Active",
    },
    {
      name: "Sophia Thomas",
      eid: "A105",
      approverLevel: "Level 2",
      type: "Manager",
      organization: "Org2",
      jobTitle: "Project Lead",
      startDate: "2023-05-01",
      endDate: "2023-08-31",
      status: "Inactive",
    },
    {
      name: "Ethan Moore",
      eid: "A106",
      approverLevel: "Level 1",
      type: "Manager",
      organization: "Org3",
      jobTitle: "Coordinator",
      startDate: "2023-06-01",
      endDate: "2023-12-31",
      status: "Active",
    },
    {
      name: "Mia Taylor",
      eid: "A107",
      approverLevel: "Level 2",
      type: "Senior Manager",
      organization: "Org4",
      jobTitle: "Team Lead",
      startDate: "2023-07-01",
      endDate: "2023-11-30",
      status: "Active",
    },
    {
      name: "William Lee",
      eid: "A108",
      approverLevel: "Level 3",
      type: "Director",
      organization: "Org5",
      jobTitle: "Director",
      startDate: "2023-08-01",
      endDate: "2023-10-31",
      status: "Active",
    },
  ];

  // Use provided availableApprovers if available; otherwise, defaultApprovers
  const approversList =
    availableApprovers && availableApprovers.length
      ? availableApprovers
      : defaultApprovers;

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedApprover, setSelectedApprover] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [show, setShow] = useState(false);

  // Smooth open animation
  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 50);
    return () => clearTimeout(timer);
  }, []);

  // Filter approvers based on the search term
  const filteredApprovers = approversList.filter((approver) =>
    approver.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleSelectApprover = (name) => {
    setSelectedApprover(name);
    setSearchTerm(name);
    setIsDropdownVisible(false);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!selectedApprover) {
      alert("Please select an approver.");
      return;
    }
    const selectedApproverObj = approversList.find(
      (a) => a.name === selectedApprover
    );
    onSubmit(selectedApproverObj);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-bold text-[#4F2683]">Add Approver</h2>
          <button
            type="button"
            className="w-8 h-8 bg-[#4F2683] text-white flex items-center justify-center rounded-full text-2xl"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
            {/* Form */}
            <form onSubmit={handleSubmit} className="bg-white p-6 pt-2 rounded-lg my-3">
              {/* Search Input with Dropdown */}
              <div className="mb-4 flex items-center">
                <label className="text-[16px] font-normal w-1/4">Select Approver</label>
                <div className="relative w-3/4">
                  <input
                    type="text"
                    placeholder="Search Approver"
                    value={searchTerm}
                    onChange={(e) => {
                      setSearchTerm(e.target.value);
                      setIsDropdownVisible(true);
                    }}
                    onFocus={() => setIsDropdownVisible(true)}
                    onBlur={() => setTimeout(() => setIsDropdownVisible(false), 150)}
                    className="w-full h-11 border border-gray-300 rounded px-3"
                  />
                  {isDropdownVisible && (
                    <div className="absolute top-full left-0 w-full mt-1 border bg-white rounded-md shadow-lg max-h-60 overflow-y-auto z-50">
                      {filteredApprovers.length > 0 ? (
                        filteredApprovers.map((approver) => (
                          <div
                            key={approver.name}
                            className="p-2 cursor-pointer hover:bg-gray-100"
                            onMouseDown={() => handleSelectApprover(approver.name)}
                          >
                            {approver.name}
                          </div>
                        ))
                      ) : (
                        <div className="p-2 text-gray-700 text-center">No Results Found.</div>
                      )}
                    </div>
                  )}
                </div>
              </div>
              {/* Action Buttons */}
              <div className="flex gap-4 justify-center">
                <Button type="button" label="Cancel" onClick={() => {
                  setShow(false);
                  setTimeout(onClose, 700);
                }} className="bg-gray-400 text-white" />
                <Button type="submit" label="Add" className="bg-[#4F2683] text-white" />
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ApproversAdd;
