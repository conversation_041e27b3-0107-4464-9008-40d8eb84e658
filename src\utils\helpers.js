export function sanitizeRequest(input) {
    if (input instanceof FormData) {
      const newFormData = new FormData();
      input.forEach((value, key) => {
        if (value !== null && value !== '') {
          newFormData.append(key, value);
        }
      });
      return newFormData;
    } else if (Array.isArray(input)) {
      // Recursively sanitize each item in the array
      return input.map((item) => sanitizeRequest(item));
    } else if (typeof input === 'object' && input !== null) {
      // Recursively sanitize each property in the object
      return Object.entries(input).reduce((acc, [key, value]) => {
        const sanitizedValue = sanitizeRequest(value);
        // Only include key if sanitizedValue is not null, empty, or an empty object
        if (
          sanitizedValue !== null &&
          sanitizedValue !== '' &&
          (typeof sanitizedValue !== 'object' || Object.keys(sanitizedValue).length > 0)
        ) {
          acc[key] = sanitizedValue;
        }
        return acc;
      }, {});
    }
    return input;
  }
  