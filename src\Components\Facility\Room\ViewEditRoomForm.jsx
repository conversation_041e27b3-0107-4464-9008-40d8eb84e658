import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import CustomDropdown from "../../Global/CustomDropdown";
import Input from "../../Global/Input/Input";
import Loader from "../../Loader.jsx";
import { toast } from "react-toastify";
import { updateRoom } from "../../../api/facility";
import { useBuildingData } from "../../../hooks/useBuildingData";
import { useFloorData } from "../../../hooks/useFloorData";
import { useRoomMasterData } from "../../../hooks/useRoomMasterData";

// Validation schema for room fields
const roomSchema = yup.object().shape({
  building_id: yup.string().required("Building is required"),
  floor_id: yup
    .string()
    .required("Floor Number is required")
    .matches(
      /^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$/,
      "Floor ID must be a valid UUID"
    ),
  room_number: yup.string().required("Room Number is required"),
  max_occupancy: yup
    .number()
    .typeError("Max Occupancy must be a number")
    .required("Max Occupancy is required"),
  primary_contact_name: yup.string().required("Primary Contact Name is required"),
  primary_contact_email: yup
    .string()
    .email("Enter a valid email")
    .required("Primary Contact Email is required"),
  primary_contact_number: yup.string().required("Primary Contact Number is required"),
  area: yup
    .number()
    .typeError("Area must be a number")
    .required("Area is required"),
  status: yup.number().required("Status is required"),
  building_code: yup.string().required("Building Code is required"),
});

const ViewEditRoomForm = ({ roomData, fetchRooms, onClose }) => {
  console.log(roomData) 
  const [isEditMode, setIsEditMode] = useState(false);
  const [show, setShow] = useState(false);
  const { statusOptions } = useRoomMasterData();
  const buildingOptions = useBuildingData(roomData.facility_id);
  const [selectedBuildingId, setSelectedBuildingId] = useState(roomData.building_id || "");
  const floorOptions = useFloorData(selectedBuildingId);

  const {
    register,
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors },
  } = useForm({
    resolver: yupResolver(roomSchema),
    defaultValues: {
      room_id: roomData.room_id || "",
      building_id: roomData.building_id || "",
      floor_id: roomData.floor_id || "",
      room_number: roomData.room_number || "",
      max_occupancy: roomData.max_occupancy || "",
      primary_contact_name: roomData.primary_contact_name || "",
      primary_contact_email: roomData.primary_contact_email || "",
      primary_contact_number: roomData.primary_contact_number || "",
      area: roomData.area || "",
      status: roomData.status,
      building_code: roomData.building?.building_code || roomData.building_code || "",
      facility_id: roomData.facility_id || "",
    },
  });

  // Reset form when roomData changes
  useEffect(() => {
    reset({
      room_id: roomData.room_id || "",
      building_id: roomData.building_id || "",
      floor_id: roomData.floor_id || "",
      room_number: roomData.room_number || "",
      max_occupancy: roomData.max_occupancy || "",
      primary_contact_name: roomData.primary_contact_name || "",
      primary_contact_email: roomData.primary_contact_email || "",
      primary_contact_number: roomData.primary_contact_number || "",
      area: roomData.area || "",
      status: roomData.status,
      building_code: roomData.building?.building_code || roomData.building_code || "",
      facility_id: roomData.facility_id || "",
    });
    setSelectedBuildingId(roomData.building_id || "");
  }, [roomData, reset]);

  // Animation mount/unmount logic
  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  // Field configuration array
  const roomFields = [
    {
      label: "Facility",
      name: "facility_id",
      type: "text",
      isReadOnly: true,
      viewValue: roomData.facility?.name || roomData.facility_id,
    },
    {
      label: "Building",
      name: "building_id",
      type: "dropdown",
      options: buildingOptions,
      viewValue: roomData.building.name,
    },
    {
      label: "Floor Number",
      name: "floor_id",
      type: "dropdown",
      options: floorOptions,
      viewValue: roomData.floor.floor_number,
    },
    {
      label: "Room Number",
      name: "room_number",
      type: "text",
    },
    {
      label: "Max Occupancy",
      name: "max_occupancy",
      type: "number",
    },
    {
      label: "Primary Contact Name",
      name: "primary_contact_name",
      type: "text",
    },
    {
      label: "Primary Contact Email",
      name: "primary_contact_email",
      type: "email",
    },
    {
      label: "Primary Contact Number",
      name: "primary_contact_number",
      type: "tel",
    },
    {
      label: "Area",
      name: "area",
      type: "number",
    },
    {
      label: "Status",
      name: "status",
      type: "dropdown",
      options: statusOptions,
      viewValue: roomData.room_status_name?.value || roomData.status,
    },
    {
      label: "Building Code",
      name: "building_code",
      type: "text",
      isReadOnly: true,
      viewValue: roomData.building.building_code,
    },
  ];

  // Form submission handler: update the room using API call
  const onSubmit = async (data) => {
    try {
      const { facility_id, room_id, building_code, ...payload } = data;
      await updateRoom(facility_id, room_id, payload);
      toast.success("Room updated successfully!");
      await fetchRooms();
      setIsEditMode(false);
      onClose();
    } catch (error) {
      toast.error(
        error.response && error.response.data
          ? error.response.data.message
          : "Failed to update room. Please try again."
      );
    }
  };

  // Cancel editing: reset form values to original data
  const handleCancelEdit = () => {
    reset({
      room_id: roomData.room_id || "",
      building_id: roomData.building_id || "",
      floor_id: roomData.floor_id || "",
      room_number: roomData.room_number || "",
      max_occupancy: roomData.max_occupancy || "",
      primary_contact_name: roomData.primary_contact_name || "",
      primary_contact_email: roomData.primary_contact_email || "",
      primary_contact_number: roomData.primary_contact_number || "",
      area: roomData.area || "",
      status: roomData.status,
      building_code: roomData.building?.building_code || roomData.building_code || "",
      facility_id: roomData.facility_id || "",
    });
    setIsEditMode(false);
    setSelectedBuildingId(roomData.building_id || "");
  };

  return (
   <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">View/Edit Room</h2>
           <button
    className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
    type="button"
    onClick={() => {
      setShow(false);
      setTimeout(onClose, 700);
    }}
  >
    &times;
  </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

        {/* Form */}
        <form onSubmit={handleSubmit(onSubmit)} className="p-6">
          {roomFields.map((field, idx) => (
            <div key={idx} className="flex items-center mb-4">
              <label className="w-1/3 text-[16px] font-normal">{field.label}</label>
              <div className="w-2/3">
                {isEditMode && !field.isReadOnly ? (
                  field.type === "dropdown" ? (
                    <Controller
                      control={control}
                      name={field.name}
                      render={({ field: controllerField }) => (
                        <CustomDropdown
                          key={controllerField.value}
                          className="h-11 rounded border-gray-300"
                          placeholder={field.placeholder || `Select ${field.label}`}
                          options={field.options}
                          onSelect={(option) => {
                            if (field.name === "building_id") {
                              setSelectedBuildingId(option);
                              controllerField.onChange(option);
                              setValue("floor_id", "");
                              const selectedBuilding = buildingOptions.find(
                                (b) => b.value === option
                              );
                              setValue(
                                "building_code",
                                selectedBuilding ? selectedBuilding.code || "" : ""
                              );
                            } else {
                              controllerField.onChange(option);
                            }
                          }}
                          selectedOption={controllerField.value}
                          value={controllerField.value}
                          hoverBgColor="hover:bg-[#4F2683]"
                          borderColor="border-gray-300"
                        />
                      )}
                    />
                  ) : (
                    <Input
                      type={field.type}
                      {...register(field.name)}
                      disabled={!isEditMode}
                      className="w-full border rounded p-2"
                    />
                  )
                ) : (
                  <Input
                    type={field.type === "dropdown" ? "text" : field.type}
                    value={
                      field.type === "dropdown"
                        ? field.viewValue
                        : roomData[field.name] || ""
                    }
                    disabled
                    className="w-full border-none text-[#8F8F8F]"
                  />
                )}
                {errors[field.name] && (
                  <p className="text-red-500 text-sm">{errors[field.name].message}</p>
                )}
              </div>
            </div>
          ))}

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            {!isEditMode ? (
              <button
                type="button"
                onClick={() => setIsEditMode(true)}
                className="px-4 py-2 bg-[#4F2683] text-white rounded"
              >
                Edit
              </button>
            ) : (
              <>
                <button
                  type="button"
                  onClick={handleCancelEdit}
                  className="px-4 py-2 bg-[#979797] text-white rounded"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="px-4 py-2 bg-[#4F2683] text-white rounded"
                >
                  Save
                </button>
              </>
            )}
          </div>
        </form>
        </div>
        </div>
      </div>
    </div>
  );
};

export default ViewEditRoomForm;