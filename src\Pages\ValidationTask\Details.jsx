import React, { useState } from 'react';
import Organization from '../../Components/IdentityHub/Organization/index';
import DetailsCard from '../../Components/Global/DetailsCard';
import EditPhotoModal from '../../Components/Global/ImageAndCamera/EditPhotoModal';
import userImg from '../../Images/Building.svg'
import { useNavigate } from 'react-router-dom';
import { IoIosArrowBack } from "react-icons/io";
import HistoryTable from '../../Components/Observation/HistoryTable';
import { useSearchParams } from "react-router-dom";
import ValidationDetails from '../../Components/ValidationTask/ValidationDetails';
import Tasks from '../../Components/ValidationTask/Tasks';

const Details = () => {
  const [searchParams] = useSearchParams();
  const name = searchParams.get("name");
  const [selectedTab, setSelectedTab] = useState('Validation Details');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [profileImage, setProfileImage] = useState(null);
  const navigate = useNavigate();
  const [isHistoryPanelOpen, setIsHistoryPanelOpen] = useState(false);

  const handleHistoryOpen = () => setIsHistoryPanelOpen(true);

  // Function to handle image capture
  const handleImageCaptured = (imageSrc) => {
    setProfileImage(imageSrc); 
    setIsModalOpen(false);
  };
  const Options =
  ['Validation Details', 'Task',]


  return (
    <div className="bg-gray-100 min-h-screen p-8 pl-20 pt-20">
      {/* Profile Section */}
      <div className="flex items-center text-[#4F2683]">
        <div className='flex items-center gap-1 cursor-pointer' onClick={() => navigate('/validation-task')}>
          <IoIosArrowBack className="text-[#4F2683] font-normal text-[24px]" />
          <h2 className="font-normal text-[24px]">Validation Task</h2>
        </div>
      </div>
      <DetailsCard
      OpenPhotoModal={() => setIsModalOpen(true)}
      handleHistoryOpen={handleHistoryOpen}
      profileImage={profileImage}
      defaultImage={userImg}
      name={name}
      showHistoryButton={true}
      additionalFields={[
        { label: "Task Type", value: "Area Owner Validation " },
        { label: "Task Id", value: "1590" },
        { label: "Status", value: "Pending" },
    ]}
      />

      {/* Sidebar and Main Content */}
      <div className="flex">
        <div className="w-[12%] mt-4">
          {Options.map((tab) => (
            <button
              key={tab}
              className={`block w-full text-left p-2 mb-2 ${selectedTab === tab ? 'text-[#4F2683] border-l-2 border-[#4F2683]' : 'text-gray-700'}`}
              onClick={() => setSelectedTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>

        {/* Main Content */}
        <div className="w-[88%] ">
          {selectedTab === 'Validation Details' && <ValidationDetails  />}
          {selectedTab === 'Task' && <Tasks />}
         

        </div>
      </div>
      <HistoryTable
        isOpen={isHistoryPanelOpen}
        onClose={() => setIsHistoryPanelOpen(false)} />
      {isModalOpen && (
        <EditPhotoModal
          // title={modalTitle}
          onClose={() => setIsModalOpen(false)}
          onSave={handleImageCaptured}
        />
      )}
    </div>
  );
};

export default Details;
