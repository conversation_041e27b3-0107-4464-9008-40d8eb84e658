import React, { useEffect, useState } from "react";
import Button from "../../Global/Button";
import Input from "../../Global/Input/Input";
import CustomDropdown from "../../Global/CustomDropdown";
import DateInput from "../../Global/Input/DateInput";
import { useCardData } from "../../../hooks/useCardData"; // ✅ Correct import
import { createCard } from "../../../api/identity";
import { toast } from "react-toastify";
import { useLocation } from "react-router-dom"; // Import useLocation
const AddCardForm = ({ onSubmit, onClose }) => {
  const { statusOptions, formatOptions, templateOptions } = useCardData();

  // Use useLocation to fetch query parameters
  const location = useLocation();
  const queryParams = new URLSearchParams(location.search);
  const identityId = queryParams.get("identity_id"); // Fetch identity_id from query parameters
  const [cardNumber, setCardNumber] = useState("");
  const [cardFormat, setCardFormat] = useState("");
  const [facilityCode, setFacilityCode] = useState("");
  const [pin, setPin] = useState("");
  const [template, setTemplate] = useState("");
  const [status, setStatus] = useState("");
  const [activationDate, setActivationDate] = useState("");
  const [deactivationDate, setDeactivationDate] = useState("");
  const [reason, setReason] = useState("");
  const [show, setShow] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    const getFirstValue = (options) => {
      if (!options || options.length === 0) return "";
      return typeof options[0] === "object"
        ? options[0].value || options[0].id || options[0]
        : options[0];
    };

    if (formatOptions?.length > 0 && !cardFormat) {
      setCardFormat(getFirstValue(formatOptions));
    }
    if (templateOptions?.length > 0 && !template) {
      setTemplate(getFirstValue(templateOptions));
    }
    if (statusOptions?.length > 0 && !status) {
      setStatus(getFirstValue(statusOptions));
    }
  }, [formatOptions, templateOptions, statusOptions]);

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields and show specific field names in toast
    const requiredFields = [];

    if (!cardNumber) {
      requiredFields.push("Card Number");
    }
    if (!cardFormat) {
      requiredFields.push("Card Format");
    }
    if (!template) {
      requiredFields.push("Template");
    }

    if (!identityId) {
      requiredFields.push("Identity ID");
    }

    if (requiredFields.length > 0) {
      const fieldNames = requiredFields.join(", ");
      toast.error(`Please fill the following required field(s): ${fieldNames}`);
      return;
    }

    const newCard = {
      card_number: cardNumber,
      card_format: typeof cardFormat === "object" ? cardFormat.value : cardFormat,
      facility_code: facilityCode || null, // Send null if empty
      pin,
      template: typeof template === "object" ? template.value : template,
      status: typeof status === "object" ? status.value : status,
      active_date: activationDate,
      deactive_date: deactivationDate,
      reason,
      identity_id: identityId, // Include identity_id here
    };

    try {
      const result = await createCard(newCard);
      toast.success("Card created successfully!");
      onSubmit?.(result);
      onClose();
      // Reset form
      setCardNumber("");
      setCardFormat("");
      setFacilityCode("");
      setPin("");
      setTemplate("");
      setStatus("");
      setActivationDate("");
      setDeactivationDate("");
      setReason("");
    } catch (error) {
      console.error(error);
      toast.error("Failed to create card.");
    }
  };
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >

        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-[24px] md:text-[30px] font-normal text-[#4F2683]">
            Add Card
          </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>

        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">
            <form onSubmit={handleSubmit} className="p-6">

              <h2 className="text-[20px] text-[#333333] font-medium pb-4">
                Card Details
              </h2>
              {/* Card Number */}
              <FormRow label="Card Number*" id="cardNumber">
                <Input
                  type="text"
                  value={cardNumber}
                  onChange={(e) => setCardNumber(e.target.value)}
                  required
                />
              </FormRow>
              {/* Card Format */}
              <FormRow label="Card Format*" id="cardFormat">
                <CustomDropdown
                  options={formatOptions.map((opt) => ({
                    label: opt.name || opt.label || opt,
                    value: opt.value || opt.id || opt,
                  }))}
                  placeholder="Select Card Format"
                  selectedOption={cardFormat}
                  onSelect={(val) => setCardFormat(val)}
                  className="h-11 rounded"
                  borderColor="border-gray-300"
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              </FormRow>
              {/* Facility Code */}
              <FormRow label="Facility Code" id="facilityCode">
                <Input
                  type="text"
                  value={facilityCode}
                  onChange={(e) => setFacilityCode(e.target.value)}
                />
              </FormRow>
              {/* Pin */}
              <FormRow label="Pin*" id="pin">
                <Input
                  type="text"
                  value={pin}
                  onChange={(e) => setPin(e.target.value)}
                  required
                />
              </FormRow>
              {/* Template */}
              <FormRow label="Template*" id="template">
                <CustomDropdown
                  options={templateOptions.map((opt) => ({
                    label: opt.name || opt.label || opt,
                    value: opt.value || opt.id || opt,
                  }))}
                  placeholder="Select Template"
                  selectedOption={template}
                  onSelect={(val) => setTemplate(val)}
                  className="h-11 rounded"
                  borderColor="border-gray-300"
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              </FormRow>
              {/* Status */}
              <FormRow label="Status" id="status">
                <CustomDropdown
                  options={statusOptions}
                  placeholder="Select Status"
                  selectedOption={status}
                  onSelect={(val) => setStatus(val)}
                  className="h-11 rounded"
                  borderColor="border-gray-300"
                  hoverBgColor="hover:bg-[#4F2683]"
                />
              </FormRow>
              {/* Activation Date */}
              <FormRow label="Activation Date*" id="activationDate">
                <DateInput
                  id="activationDate"
                  value={activationDate}
                  onChange={(date) =>
                    setActivationDate(
                      date ? date.toLocaleDateString("en-CA").split("T")[0] : ""
                    )
                  }
                  placeholder="MM-DD-YYYY"
                />
              </FormRow>
              {/* Deactivation Date */}
              <FormRow label="Deactivation Date" id="deactivationDate">
                <DateInput
                  id="deactivationDate"
                  value={deactivationDate}
                  onChange={(date) =>
                    setDeactivationDate(
                      date ? date.toLocaleDateString("en-CA").split("T")[0] : ""
                    )
                  }
                  placeholder="MM-DD-YYYY"
                />
              </FormRow>
              {/* Reason */}
              <FormRow label="Reason" id="reason">
                <Input
                  type="text"
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                />
              </FormRow>
              <div className="flex gap-4 pb-4 justify-center">
                <Button
                  type="cancel"
                  label="Cancel"
                  onClick={onClose}
                  className="bg-gray-400 text-white"
                />
                <Button type="primary" label="Add" className="text-white" />
              </div>
            </form>
          </div>
        </div>

      </div>
    </div>
  );
};
// Reusable form row component
const FormRow = ({ label, id, children }) => (
  <div className="flex items-center mb-4">
    <label
      htmlFor={id}
      className="w-1/4 text-[16px] font-normal text-[#333333]"
    >
      {label}
    </label>
    <div className="w-3/4">{children}</div>
  </div>
);
export default AddCardForm;
