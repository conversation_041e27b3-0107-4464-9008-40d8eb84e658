import React, { useState, useEffect } from "react";
import { IoClose } from "react-icons/io5";

const Notifition = ({ isOpen, onClose, notifications = [] }) => {
  const [activeTab, setActiveTab] = useState("All");
  const [notifs, setNotifs] = useState([]);
  const [show, setShow] = useState(false);

  useEffect(() => {
    setNotifs(notifications);
  }, [notifications]);

  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => setShow(true), 10);
      return () => clearTimeout(timer);
    } else {
      setShow(false);
    }
  }, [isOpen]);

  const handleClose = () => {
    setShow(false);
    setTimeout(onClose, 700);
  };

  const handleConfirm = (id) => {
    setNotifs((prev) => prev.filter((n) => n.id !== id));
  };

  const handleDelete = (id) => {
    setNotifs((prev) => prev.filter((n) => n.id !== id));
  };

  const filteredNotifs = activeTab === "All" ? notifs : notifs.filter((n) => n.unread);

  return (
    <div className={`fixed inset-0 z-50 ${isOpen ? "" : "hidden"}`}>
      <div className="fixed inset-0 bg-black bg-opacity-30" onClick={handleClose}></div>
      <div className={`fixed top-0 right-0 h-full w-[700px] max-w-full rounded-l-md bg-white shadow-lg z-50 flex flex-col transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`} style={{ willChange: "transform" }}>
        <div className="flex  items-center justify-between px-5 py-4 border-b">
          <div className="flex gap-4">
            <button
              className={`font-semibold text-[18px] pb-2 ${
                activeTab === "All" ? "border-b-2 border-[#4F2683] text-[#4F2683]" : "text-[#7C7C7C]"
              }`}
              onClick={() => setActiveTab("All")}
            >
              All
            </button>
            <button
              className={`font-semibold text-[18px] pb-2 ${
                activeTab === "Unread" ? "border-b-2 border-[#4F2683] text-[#4F2683]" : "text-[#7C7C7C]"
              }`}
              onClick={() => setActiveTab("Unread")}
            >
              Unread
            </button>
          </div>
          <button className="p-1 bg-[#4F2683] rounded-full" onClick={handleClose}>
            <IoClose className="text-white text-lg" />
          </button>
        </div>

        <div className="px-5 py-3 border-b flex items-center justify-between bg-[#F0F2F5]">
          <div>
            <span className="font-semibold text-[15px]">Your push notifications are off</span>
            <div className="text-xs text-[#7C7C7C]">Turn on notifications to stay connected</div>
          </div>
          <button className="ml-auto text-[#7C7C7C] text-lg" onClick={handleClose}>×</button>
        </div>

        <div className="px-5 py-2 flex items-center justify-between">
          <span className="font-semibold text-[16px]">New</span>
          <button className="text-[#4F2683] text-sm font-medium">See all</button>
        </div>

        <div className="flex-1 overflow-y-auto pb-2">
          {filteredNotifs.length === 0 && (
            <div className="text-center text-[#7C7C7C] py-8">No notifications</div>
          )}
          {filteredNotifs.map((notif) => (
            <div key={notif.id} className="flex items-start gap-3 px-5 py-3 hover:bg-[#F0F2F5] rounded-lg relative">
              <img
                src={
                  notif.avatar ||
                  "https://ui-avatars.com/api/?name=" +
                    encodeURIComponent(notif.name) +
                    "&background=EEE9F2&color=4F2683"
                }
                alt={notif.name}
                className="w-10 h-10 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="text-[15px]">
                  <span className="font-semibold">{notif.name}</span> {notif.message}
                </div>
                <div className="text-xs text-[#7C7C7C] flex items-center gap-2">
                  {notif.time} • {notif.mutual} mutual friends
                </div>
                <div className="flex gap-2 mt-2">
                  <button
                    className="bg-[#4F2683] text-white px-4 py-1 rounded-md text-sm"
                    onClick={() => handleConfirm(notif.id)}
                  >
                    Confirm
                  </button>
                  <button
                    className="bg-[#E4E6EB] text-[#4F2683] px-4 py-1 rounded-md text-sm"
                    onClick={() => handleDelete(notif.id)}
                  >
                    Delete
                  </button>
                </div>
              </div>
              {notif.unread && (
                <span className="absolute top-4 right-2 w-2 h-2 bg-[#1877F2] rounded-full"></span>
              )}
            </div>
          ))}
        </div>

        <div className="px-5 py-3 flex gap-2">
          <button className="bg-[#4F2683] text-white px-4 py-2 rounded-lg flex-1">Turn on</button>
          <button className="bg-[#E4E6EB] text-[#4F2683] px-4 py-2 rounded-lg flex-1">Not now</button>
        </div>
      </div>
    </div>
  );
};

export default Notifition;
