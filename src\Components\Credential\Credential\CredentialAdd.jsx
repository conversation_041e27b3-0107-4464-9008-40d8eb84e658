import React from "react";
import { useSelector } from "react-redux";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import Input from "../../Global/Input/Input";
import Button from "../../Global/Button";
import DateTimeInput from "../../Temporary/DateTimeInput";
import SearchableDropdown from "../../Global/SearchableDropdownIdentity";
import CustomDropdown from "../../Global/CustomDropdown";

const statusOptions = [
  { label: "Unprinted", value: "Unprinted" },
  { label: "Printed", value: "Printed" },
  { label: "In Queue", value: "Badges in Queue" },
  { label: "Completed", value: "Completed" },
  { label: "Pending Photo Upload", value: "Pending Photo Upload" },
  { label: "Completed Photo Upload ", value: "Completed Photo Upload" },
  { label: "Print Failed Badges", value: "Print Failed Badges" },
  ];

const templateOptions = [
  { label: "Employee Badge-RWC-Remote Pri", value: "Employee Badge-RWC-Remote Pri" },
  { label: "Employee Badge Template", value: "Employee Badge Template" },
  { label: "Contractor Badge Template", value: "Contractor Badge Template" },
];

const AddCredentialModal = ({ onClose, onAdd, initialValues: editValues }) => {
  const selectedFacility = useSelector((state) => state.facility.selected);

  const defaultInitialValues = {
    searchIdentity: { name: "", eid: "", type: "", image: "" },
    cardFormat: "",
    cardNumber: "",
    createdOn: new Date(),
    decreatedOn: "",
    cardType: "Temporary Card",
    facilityCode: selectedFacility,
    reason: "",
    status: "",
    badgeTemplate: "",
  };

  const initialValues = editValues || defaultInitialValues;

  const validationSchema = Yup.object().shape({
    searchIdentity: Yup.object().shape({
      name: Yup.string().required("Search Identity is required"),
      eid: Yup.string().required("Identity EID is required"),
    }),
    cardFormat: Yup.string().required("Card Format is required"),
    createdOn: Yup.date().required("Activation Date/Time is required"),
    status: Yup.string().required("Card Status is required"),
  });

  const handleSubmit = (values) => {
    const activationStr = values.createdOn.toLocaleString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
    const deactivationStr = values.decreatedOn.toLocaleString("en-US", {
      day: "2-digit",
      month: "short",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });

    const newCard = {
      image: values.searchIdentity.image,
      name: values.searchIdentity.name,
      eid: values.searchIdentity.eid,
      cardFormat: values.cardFormat,
      cardNumber: values.cardNumber,
      facilityCode: values.facilityCode,
      pin: values.pin,
      badgeTemplate: values.badgeTemplate,
      createdOn: activationStr,
      decreatedOn: deactivationStr,
      type: values.searchIdentity.type,
      cardType: values.cardType,
      status: values.status,
      reason: values.reason,
      shipmentAddress: "Not Avaliable",
    };

    onAdd(newCard);
  };
  const cardTypeOptions = [
    { label: "Temporary Card", value: "Temporary Card" },
    { label: "Permanent Card", value: "Permanent Card" },
  ]

  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    const timer = setTimeout(() => setShow(true), 10);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-end z-50">
      <div
        className={`bg-[#f1eef5] w-full h-full max-w-5xl rounded-l-[20px] shadow-lg overflow-y-auto transform transition-transform duration-700 ease-in-out ${show ? "translate-x-0" : "translate-x-full"}`}
        style={{ willChange: "transform" }}
      >
        <div className="flex items-center bg-white justify-between shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid px-6 py-4">
          <h2 className="text-2xl font-semibold text-[#4F2683]">Add/View/Edit credential </h2>
          <button
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full"
            type="button"
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
          >
            &times;
          </button>
        </div>
        <div className="p-6">
          <div className="shadow-[0_4px_8px_5px_rgba(79,38,131,0.06)] border border-[#4F2683]/[0.24] border-solid bg-white rounded-[15px]">

            <Formik
              initialValues={initialValues}
              enableReinitialize
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({ values, errors, touched, setFieldValue }) => (
                <Form className="p-6">
                  <div className="space-y-4">
                    {/* Search Identity Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Search Identity *</label>
                      <div className="w-2/3">
                        <SearchableDropdown
                          value={values.searchIdentity?.name || ""}
                          onSelect={(selected) =>
                            setFieldValue("searchIdentity", selected)
                          }
                        />
                        {touched.searchIdentity?.name && errors.searchIdentity?.name && (
                          <div className="text-red-500 text-sm">
                            {errors.searchIdentity.name}
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Card Format Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Card Format *</label>
                      <div className="w-2/3">
                        <Input
                          name="cardFormat"
                          placeholder="Card Format"
                          value={values.cardFormat}
                          onChange={(e) =>
                            setFieldValue("cardFormat", e.target.value)
                          }
                        />
                        {touched.cardFormat && errors.cardFormat && (
                          <div className="text-red-500 text-sm">{errors.cardFormat}</div>
                        )}
                      </div>
                    </div>
                    {/* Card Number Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Card Number</label>
                      <div className="w-2/3">
                        <Input
                          type="number"
                          name="cardNumber"
                          placeholder="Card Number"
                          value={values.cardNumber}
                          onChange={(e) => setFieldValue("cardNumber", e.target.value)}
                        />
                        {touched.cardNumber && errors.cardNumber && (
                          <div className="text-red-500 text-sm">{errors.cardNumber}</div>
                        )}
                      </div>
                    </div>
                    {/* Facility/Building Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Facility Code</label>
                      <div className="w-2/3">
                        <Input
                          name="facilityCode"
                          placeholder="Facility/Building"
                          value={values.facilityCode}
                          readOnly={true}
                          onChange={(e) =>
                            setFieldValue("facilityCode", e.target.value)
                          }
                        />
                        {touched.facilityCode && errors.facilityCode && (
                          <div className="text-red-500 text-sm">
                            {errors.facilityCode}
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Pin Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Pin</label>
                      <div className="w-2/3">
                        <Input
                          type="number"
                          name="pin"
                          placeholder="Pin"
                          value={values.pin}
                          onChange={(e) => setFieldValue("pin", e.target.value)}
                        />
                        {touched.pin && errors.pin && (
                          <div className="text-red-500 text-sm">{errors.pin}</div>
                        )}
                      </div>
                    </div>
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Template</label>
                      <div className="w-2/3">
                        <CustomDropdown
                          options={templateOptions}
                          placeholder="Template"
                          value={values.badgeTemplate}
                          className="h-10"
                          onSelect={(selectedValue) =>
                            setFieldValue("badgeTemplate", selectedValue)
                          }
                          error={touched.badgeTemplate && errors.badgeTemplate ? { message: errors.badgeTemplate } : null}
                        />
                      </div>
                    </div>
                    {/* Activation Date/Time Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Activation Date/Time *</label>
                      <div className="w-2/3">
                        <DateTimeInput
                          name="createdOn"
                          value={values.createdOn}
                          onChange={(date) =>
                            setFieldValue("createdOn", date)
                          }
                          placeholder="Select activation date & time"
                        />
                        {touched.createdOn && errors.createdOn && (
                          <div className="text-red-500 text-sm">
                            {errors.createdOn}
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Deactivation Date/Time Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Deactivation Date/Time</label>
                      <div className="w-2/3">
                        <DateTimeInput
                          name="decreatedOn"
                          value={values.decreatedOn}
                          onChange={(date) =>
                            setFieldValue("decreatedOn", date)
                          }
                          placeholder="Select deactivation date & time"
                        />
                        {touched.decreatedOn && errors.decreatedOn && (
                          <div className="text-red-500 text-sm">
                            {errors.decreatedOn}
                          </div>
                        )}
                      </div>
                    </div>
                    {/* Card Status Field using CustomDropdown */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Card Type</label>
                      <div className="w-2/3">
                        <CustomDropdown
                          options={cardTypeOptions}
                          placeholder="Card Type"
                          value={values.cardType}
                          className="h-10"
                          onSelect={(selectedValue) =>
                            setFieldValue("cardType", selectedValue)
                          }
                          error={touched.cardType && errors.cardType ? { message: errors.cardType } : null}
                        />
                      </div>
                    </div>

                    {/* Card Status Field using CustomDropdown */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Card Status *</label>
                      <div className="w-2/3">
                        <CustomDropdown
                          options={statusOptions}
                          placeholder="Select Card Status"
                          value={values.status}
                          className="h-10"
                          onSelect={(selectedValue) =>
                            setFieldValue("status", selectedValue)
                          }
                          error={touched.status && errors.status ? { message: errors.status } : null}
                        />
                      </div>
                    </div>

                    {/* Justification Field */}
                    <div className="flex mb-2 items-center">
                      <label className="mr-2 w-1/3">Reason</label>
                      <div className="w-2/3">
                        <Input
                          name="reason"
                          type="bubbles"
                          placeholder="Reason"
                          value={values.reason}
                          height="94px"
                          bubbles={true}
                          bubbleOptions={[
                            "Lost Permanent Card",
                            "Forgot Permanent Card",
                          ]}
                          onChange={(e) =>
                            setFieldValue("reason", e.target.value)
                          }
                        />
                        {touched.reason && errors.reason && (
                          <div className="text-red-500 text-sm">
                            {errors.reason}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-center gap-4 mt-6">
                    <Button type="cancel" label="Cancel" onClick={onClose} />
                    <Button type="primary" label={editValues ? "Update" : "Add"} buttonType="submit" />
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddCredentialModal;
