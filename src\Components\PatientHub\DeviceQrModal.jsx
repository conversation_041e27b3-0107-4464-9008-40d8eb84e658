import React, { useRef } from "react";
import { QRCodeCanvas } from "qrcode.react";
import jsPDF from "jspdf";
import html2canvas from "html2canvas";
import { IoClose } from "react-icons/io5";

const DeviceQrModal = ({ open, device, onClose }) => {
  const qrRef = useRef();

  // Animation state for side panel
  const [show, setShow] = React.useState(false);

  React.useEffect(() => {
    if (open) {
      const timer = setTimeout(() => setShow(true), 10);
      return () => clearTimeout(timer);
    } else {
      setShow(false);
    }
  }, [open]);

  if (!open || !device) return null;

  const handleDownload = async () => {
    const input = qrRef.current;
    const canvas = await html2canvas(input);
    const imgData = canvas.toDataURL("image/png");
    const pdf = new jsPDF();
    const imgProps = pdf.getImageProperties(imgData);
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    pdf.addImage(imgData, "PNG", 0, 0, pdfWidth, pdfHeight);
    pdf.save(`${device.device || "device"}-qr.pdf`);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex  items-center justify-end z-50">
      <div
        className={`bg-[#F5F5F7] w-[70%] rounded shadow-lg transform transition-transform duration-700 ease-in-out max-w-3xl h-full flex flex-col ${
          show ? "translate-x-0" : "translate-x-full"
        }`}
        style={{ willChange: "transform" }}
      >
        {/* Header */}
        <div className="flex items-center shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] bg-white justify-between px-5 py-5">
          <h2 className="text-lg font-semibold text-[#333]">
            Download <span className="text-[#9B51E0]">/ Scan Qr Code</span>
          </h2>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="w-8 h-8 text-2xl bg-[#4F2683] text-white rounded-full flex items-center justify-center"
            type="button"
          >
            <IoClose />
          </button>
        </div>
        <hr className="mb-4" />

        {/* Content */}
        <div
          ref={qrRef}
          className="px-6 py-4 flex rounded-lg flex-col m-4 text-sm text-gray-700 bg-[#fff] flex-1 overflow-y-auto"
        >
          {/* Title */}
          <h3 className="text-center text-[#4F2683] text-xl font-bold mb-1">
            Stellar<span className="font-normal">Care</span>
          </h3>

          {/* Subtitle */}
          <p className="text-center text-sm text-gray-600 mb-4">
            <b>Configuring Kiosk App</b>
            <br />
            Setting up your StellarCare Kiosk App is a seamless process designed to get
            you started quickly and efficiently. Follow the simple steps below:
          </p>

          {/* Step 1 */}
          <div className="mb-4">
            <h4 className="text-[#4F2683] font-bold mb-1">Step 1: Download The App</h4>
            <div className="bg-[#F9F9F9] border border-[#dcd3f3] rounded p-3 text-[13px]">
              <ul className="list-disc list-inside space-y-1">
                <li>
                  Visit the App Store for iOS devices or the Play Store for Android devices.
                </li>
                <li>
                  Search <b>StellarCare Kiosk</b> OR download the app directly:
                </li>
              </ul>
              <div className="flex justify-center mt-3 gap-4">
                <QRCodeCanvas value="https://apple.com/store/stellarcare" size={64} />
                <QRCodeCanvas value="https://play.google.com/store/apps/stellarcare" size={64} />
              </div>
            </div>
          </div>

          {/* Step 2 */}
          <div className="mb-4">
            <h4 className="text-[#4F2683] font-bold mb-1">Step 2: Scan The QR Code On The Kiosk</h4>
            <div className="flex justify-between items-start">
              <p className="text-sm w-[70%]">
                Once the app is installed, locate the QR code displayed on your StellarCare Kiosk.
                Open the app and use its built-in scanner functionality to scan the code.
              </p>
              <QRCodeCanvas
                value={JSON.stringify(device)}
                size={80}
                className="border shadow p-1 rounded"
              />
            </div>
          </div>

          {/* Step 3 */}
          <div className="mb-2">
            <h4 className="text-[#4F2683] font-bold mb-1">Step 3: Auto Configuration</h4>
            <p className="text-sm">
              After scanning the QR code, your StellarCare Kiosk will automatically configure itself.
              There's no need for manual settings — the system takes care of everything.
              Enjoy using StellarCare for a streamlined, hassle-free experience!
            </p>
          </div>
        </div>

        {/* Footer Buttons */}
        <div className="flex justify-center shadow-[0px_3.94px_7.88px_4.93px_#4F26830F] gap-4 bg-white px-6 py-3 border-t mt-2">
          <button
            onClick={() => {
              setShow(false);
              setTimeout(onClose, 700);
            }}
            className="px-5 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-100"
          >
            Cancel
          </button>
          <button
            onClick={handleDownload}
            className="px-5 py-2 bg-[#4F2683] text-white font-semibold rounded-md hover:bg-[#3a1c5c]"
          >
            Download
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeviceQrModal;
