import React, { useState, useEffect, useCallback } from "react";
import GenericTable from "../../GenericTable";
import { useParams } from "react-router-dom";
import AddRoomForm from "./AddRoomForm";
import ViewEditRoomForm from "./ViewEditRoomForm";
import { getRoomsByRooms } from "../../../api/facility";
import Loader from "../../Loader.jsx";
import { toast } from "react-toastify";
import { Tooltip } from "react-tooltip";
import TruncatedCell from "../../Tooltip/TruncatedCell.jsx";

const Room = () => {
  // Single useParams call to extract both facilityId and floorId
  const { facilityId, floorId } = useParams();

  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [showAddRoomForm, setShowAddRoomForm] = useState(false);
  const [showViewRoomForm, setShowViewRoomForm] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);

  // Updated fetchRooms: passing facilityId and floorId as params.
  const fetchRooms = useCallback(async () => {
    if (!facilityId) return;
    try {
      const response = await getRoomsByRooms(facilityId, { floorId });
      setData(response.data.data);
    } catch (error) {
      console.error("Error fetching rooms:", error);
      toast.error("Failed to load rooms.");
    } finally {
      setLoading(false);
    }
  }, [facilityId, floorId]);

  useEffect(() => {
    if (facilityId) {
      fetchRooms();
    }
  }, [facilityId, fetchRooms]);

  const handleAdd = () => {
    setShowAddRoomForm(true);
  };

  const handleCloseModal = () => {
    setShowAddRoomForm(false);
  };

  const handleView = (row) => {
    setSelectedRoom(row);
    setShowViewRoomForm(true);
  };

  const handleCloseViewModal = () => {
    setShowViewRoomForm(false);
  };

  const columns = [
    {
      name: "Room Number",
      selector: (row) => row.room_number,
      cell: (row) => (
        <span
          style={{ textDecoration: "underline", cursor: "pointer" }}
          onClick={() => handleView(row)}
        >
          {row.room_number}
        </span>
      ),
      sortable: true,
    },
    {
      name: "Max Occupancy",
      selector: (row) => row.max_occupancy,
      cell: (row) => <TruncatedCell text={String(row.max_occupancy)} />,
      sortable: true,
    },
    {
      name: "Area",
      selector: (row) => row.area,
      cell: (row) => <TruncatedCell text={String(row.area)} />,
      sortable: true,
    },
    {
      name: "Primary Contact Name",
      selector: (row) => row.primary_contact_name,
      cell: (row) => <TruncatedCell text={row.primary_contact_name} />,
      sortable: true,
    },
    {
      name: "Status",
      selector: (row) =>
        row.room_status_name ? row.room_status_name.value : row.status,
      cell: (row) => {
        const statusText = row.room_status_name
          ? row.room_status_name.value
          : row.status;
        return (
          <span
            className={`w-20 py-1 flex justify-center items-center text-sm font-semibold rounded-full ${
              statusText.toLowerCase() === "active"
                ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
                : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
            }`}
          >
            {statusText}
          </span>
        );
      },
      center: true,
    },
  ];

  const tableTitle = (
    <div className="flex items-center">
      <span>Room Details</span>
      <span
        data-tooltip-id="room-header-tooltip"
        data-tooltip-content="This table displays room details including room number, status, and more."
        className="ml-2 text-blue-500 cursor-pointer"
      >
        <i className="fas fa-info-circle" />
      </span>
    </div>
  );

  const filteredRooms =
    Array.isArray(data) && data.length > 0
      ? data.filter((room) =>
          Object.values(room || {}).some((val) =>
            String(val).toLowerCase().includes(searchTerm.toLowerCase())
          )
        )
      : [];

  return (
    <div className="bg-white rounded-[10px]  shadow-lg  w-[80%">
      {loading ? (
        <Loader />
      ) : (
        <GenericTable
          title={tableTitle}
          searchTerm={searchTerm}
          showSearch={true}
          onSearchChange={(e) => setSearchTerm(e.target.value)}
          onAdd={handleAdd}
          columns={columns}
          data={filteredRooms}
          showAddButton={true}
        />
      )}
      {showAddRoomForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className="bg-white shadow-lg p-1 rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative bg-white">
              <AddRoomForm
                floorId={floorId}
                onClose={handleCloseModal}
                fetchRooms={fetchRooms}
              />
            </div>
          {/* </div> */}
        </div>
        
      )}
      {showViewRoomForm && selectedRoom && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          {/* <div className=" rounded-lg w-[80%]"> */}
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative bg-white">
              <ViewEditRoomForm
                onClose={handleCloseViewModal}
                roomData={selectedRoom}
                fetchRooms={fetchRooms}
              />
            </div>
          {/* </div> */}
        </div>
      )}
      <Tooltip id="room-header-tooltip" place="top" effect="solid" />
    </div>
  );
};

export default Room;
