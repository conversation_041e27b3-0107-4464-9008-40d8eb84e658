import React, { useState } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import ViewRequest from "./ViewRequest";
import TrucantedRow from "../../Components/Tooltip/TrucantedRow";
import TruncatedCell from "../../Components/Tooltip/TruncatedCell";
import FilterPanel from "../../Components/Observation/FilterPanel";
import { IoFilter } from "react-icons/io5";
import { RequestHubData } from "../../api/static";

const RequestHub = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [showView, setShowView] = useState(false);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [filter, setFilter] = useState("All");
  const [isFilterPanelOpen, setIsFilterPanelOpen] = useState(false);

  const handleView = (req) => {
    setSelectedRequest(req);
    setShowView(true);
  };

  // Only externally filter by status
  const filteredData = RequestHubData.filter((row) =>
    filter === "All" ? true : row.status.toLowerCase() === "active"
  );

  const handleFilterOpen = () => setIsFilterPanelOpen(true);

  const columns = [
    {
      name: <TruncatedCell text="Request ID" />,
      selector: (row) => row.requestId,
      cell: (row) => (
        <span
          className="underline underline-offset-1 cursor-pointer"
          onClick={() => handleView(row)}
        >
          {row.requestId}
        </span>
      ),
    },
    {
      name: "Type",
      selector: (row) => row.type,
    },
    {
      name: <TruncatedCell text="Request For" />,
      selector: (row) => row.requestFor,
      cell: (row) => <TrucantedRow text={row.requestFor} />,
    },
    {
      name: <TruncatedCell text="Items" />,
      selector: (row) => row.items,
      cell: (row) => <TrucantedRow text={row.items} />,
      center: true,
    },
    {
      name: <TruncatedCell text="Justification" />,
      selector: (row) => row.justification,
      cell: (row) => <TrucantedRow text={row.justification} />,
    },
    {
      name: <TruncatedCell text="Start Date" />,
      selector: (row) => row.startDate,
    },
    {
      name: <TruncatedCell text="End Date" />,
      selector: (row) => row.endDate,
    },
    {
      name: <TruncatedCell text="Requested By" />,
      selector: (row) => row.requestedBy,
    },
    {
      name: <TruncatedCell text="Created On" />,
      selector: (row) => row.createdOn,
    },
    {
      name: "Status",
      selector: (row) => row.status,
      cell: (row) => (
        <span
          className={`w-20 py-1 flex justify-center items-center text-sm rounded-full ${
            row.status === "Active"
              ? "bg-[#4F268314] bg-opacity-8 text-[#4F2683]"
              : "bg-[#8F8F8F2B] bg-opacity-17 text-[#8F8F8F]"
          }`}
        >
          {row.status}
        </span>
      ),
    },
  ];

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <div className="mb-6">
        <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">Request Hub</h2>
      </div>
      <div className="mb-4">
        <FilterButtons
          filter={filter}
          onFilterChange={setFilter}
          filterOptions={[
            { label: "All", value: "All" },
            { label: "Active", value: "Active" },
          ]}
        />
      </div>

      <GenericTable
        title="Requests"
        searchTerm={searchTerm}
        onSearchChange={(e) => setSearchTerm(e.target.value)}
        columns={columns}
        data={filteredData}
        showSearch={true}
        showAddButton={false}
        extraControls={
          <IoFilter
            className="bg-white shadow-sm border items-center p-[5px] text-[#4F2683] h-[32px] w-8 rounded cursor-pointer"
            onClick={handleFilterOpen}
          />
        }
        // Custom filter restricts search to the requestId field only
        customFilter={(item, searchTerm) =>
          (item.requestId?.toLowerCase() || "").includes(searchTerm.toLowerCase())
        }
      />
      {showView && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center">
          <div className="bg-white p-1 shadow-lg rounded-lg">
            <div className="rounded-lg max-h-[90vh] overflow-y-auto relative">
              <ViewRequest
                requestData={selectedRequest}
                onClose={() => setShowView(false)}
              />
            </div>
          </div>
        </div>
      )}
      <FilterPanel
        isOpen={isFilterPanelOpen}
        onClose={() => setIsFilterPanelOpen(false)}
      />
    </div>
  );
};

export default RequestHub;
