import React, { useState, useRef, useEffect } from "react";
import { useTranslation } from 'react-i18next';
import TabsComponent from "../../Components/Global/TabsComponent";
import GenericTable from "../../Components/GenericTable";
import PatientCard from "../../Components/Global/PatientCard";
import PrintModal from "../../Components/Global/PrintModal";
import EditPhotoModal from "../../Components/Global/ImageAndCamera/EditPhotoModal";
import WalkInVisitForm from "../../Components/Global/Forms/WalkInVisitForm";
import AddProhibitedGuestForm from "./AddProhibitedGuestForm";
import PatientSearch from "./PatientSearch";
import GuestSearch from "./GuestSearch";
import useClickOutside from "./useClickOutside";
import { PatientCardDetails } from "../../api/static";
import Button from "../../Components/Global/Button";
import In from "../../Images/in.svg";
import Cemara from "../../Images/camera.svg";
import Chat from "../../Images/chat.svg";
import Print from "../../Images/print.svg";
import Out from "../../Images/out.svg";
import card from "../../Images/Cardidenty.svg";
import homeicon from "../../Images/home-icon.svg";
import demoimg from '../../Images/demoimg.svg';
import {
  getAppointments,
  fetchAllGuests,
  searchGuests,
  getGuestScreeningMatches,
  getDeniedGuests as apiGetDeniedGuests,
  checkGuest,
  overrideGuestScreening
} from "../../api/Appointments";
import formatDateTime from "../../utils/formatDateTime";
import CreateVisitModal from "../../Components/Global/CreateVisitModal";
import { IoMdAlert } from "react-icons/io";
import Alert from "../../Components/Global/Alert";
import InpatientVisitColumns from "./InpatientVisitColumns";
import { useSelector } from 'react-redux';

// -------------------- Main Component --------------------
// Main component for Inpatient Visit page
const InpatientVisit = () => {
  const { t } = useTranslation(); // Translation hook
   const facilityId = useSelector(state => state.facility.selectedFacility?.id);
// const facilityName = useSelector(state => state.facility.selectedFacility?.name);
  // -------------------- State Variables --------------------
  // All state variables for managing UI and data
  const [appointmentId, setAppointmentId] = useState("");
  const [patientId, setPatientId] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [data, setData] = useState([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState(null);
  const [showPatientTable, setShowPatientTable] = useState(false);
  const [guestList, setGuestList] = useState([]);
  const [guestsSecondList, setGuestsSecondList] = useState([]);
  const [guestTableSearchQuery, setGuestTableSearchQuery] = useState("");
  const [guestsSecondTableSearchQuery, setGuestsSecondTableSearchQuery] = useState("");
  const [printModalVisible, setPrintModalVisible] = useState(false);
  const [selectedGuest, setSelectedGuest] = useState(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedGuestId, setSelectedGuestId] = useState(null);
  const [showWalkInVisitForm, setShowWalkInVisitForm] = useState(false);
  const [guestSearchTerm, setGuestSearchTerm] = useState("");
  const [guestSearchResults, setGuestSearchResults] = useState([]);
  const [isGuestDropdownVisible, setIsGuestDropdownVisible] = useState(false);
  const [showAddGuestForm, setShowAddGuestForm] = useState(false);
  const [patientSearchPlaceholder, setPatientSearchPlaceholder] = useState(t('inpatient_visit.search_patient_placeholder'));
  const [guestSearchPlaceholder, setGuestSearchPlaceholder] = useState(t('inpatient_visit.search_guest_placeholder'));
  const [isCreateVisitModalOpen, setIsCreateVisitModalOpen] = useState(false);
  const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
  const [alertData, setAlertData] = useState([]);
  const [screeningId, setScreeningId] = useState('');
  const [appointmentGuestStatus, setAppointmentGuestStatus] = useState(null);
  const [shortByType, setShortByType] = useState("guest_arrival_time");
  const [shortByDirection, setShortByDirection] = useState('');
  const [deniedGuestShortByType, setDeniedGuestShortByType] = useState("");
  const [deniedGuestShortByDirection, setDeniedGuestShortByDirection] = useState('');
  const patientSearchRef = useRef(null);
  const guestSearchRef = useRef(null);

  // -------------------- Helper Functions --------------------
  // Helper to transform guest API data for table
  const transformGuest = (guest) => ({
    appointment_guest_id: guest.appointment_guest_id,
    appointment_guest_screening_id: guest.appointment_guest_screening_id ?? guest.screening_id,
    guestName: `${guest.first_name} ${guest.last_name}`,
    image: guest.guest_image,
    screening: guest.screening,
    status: guest.appointment_guest_status_name,
    guest_pin: guest.guest_pin,
    friends_and_family: guest.friends_and_family,
    mrn: guest.mrn,
    arrivalTime: guest.guest_arrival_time,
    departureTime: guest.guest_departure_time,
  });

  // Fetch guests for a given appointment/patient
  const fetchGuestsByType = async (
    appointment_id,
    guestType = 0,
    patient_id,
    sortBy = shortByType,
    sortOrder = shortByDirection,
    appointment_guest_status = appointmentGuestStatus,
     searchTerm = ""  ,
  // facility_id = facilityId        // ← add this
     
  ) => {
    try {
      const guests = await fetchAllGuests(
        appointment_id,
        guestType,
        patient_id,
        guestType === 0 ? sortBy : shortByType,
        guestType === 0 ? sortOrder : shortByDirection,
        appointment_guest_status,
        searchTerm ,
        // facility_id                      
      );
      return guests.map(transformGuest);
    } catch (error) {
      return [];
    }
  };

  // Fetch denied guests for a patient
  const getDeniedGuests = async (
    patientId,
    guestType = 2,
    sortBy = deniedGuestShortByType,
    sortOrder = deniedGuestShortByDirection
  ) => {
    try {
      const response = await apiGetDeniedGuests(patientId, guestType, sortBy, sortOrder);
      return response.data?.data?.data;
    } catch (error) {
      console.error(`Error fetching denied guests for patient ${patientId}:`, error);
      return [];
    }
  };

  // -------------------- Table Data Filtering --------------------
  // Filter denied guests by search query
  const filteredDeniedGuests = Array.isArray(guestsSecondList)
    ? guestsSecondList.filter(g =>
      g.name?.toLowerCase().includes(guestsSecondTableSearchQuery.toLowerCase())
    )
    : [];

  // -------------------- Effects --------------------
  // Close dropdowns on outside click
  useClickOutside(guestSearchRef, () => setIsGuestDropdownVisible(false));
  useClickOutside(patientSearchRef, () => setIsDropdownVisible(false));

  // Refetch guest list when appointmentGuestStatus changes
  useEffect(() => {
if (!appointmentId || !patientId) return;
    const fetchData = async () => {
      const guests = await fetchGuestsByType(
        appointmentId,
        0,
        patientId,
        shortByType,
        shortByDirection,
        appointmentGuestStatus
      );
      setGuestList(guests);
    };
    fetchData();
  }, [appointmentGuestStatus]);

  // Fetch denied/prohibited guests when patientId or denied guest sorting changes
  useEffect(() => {
    const fetchProhibitedGuests = async () => {
      if (patientId) {
        // Include the search query here too
        const prohibitedGuests = await apiGetDeniedGuests({
          patient_id: patientId,
          guest_type: 2,
          sortBy: deniedGuestShortByType,
          sortOrder: deniedGuestShortByDirection,
          search: guestsSecondTableSearchQuery,
          // facility_id: facilityId,
        });
        setGuestsSecondList(prohibitedGuests.data || []);
      }
    };
      fetchProhibitedGuests();
 }, [patientId, deniedGuestShortByType, deniedGuestShortByDirection, guestsSecondTableSearchQuery, facilityId]);

  // Fetch guest list when appointmentId, patientId, or guest sorting changes
  useEffect(() => {
    const loadGuests = async () => {
       if (appointmentId && patientId) {
        // Pass the current search query to API
        const regularGuests = await fetchGuestsByType(
          appointmentId,
          0,
          patientId,
          shortByType,
          shortByDirection,
          appointmentGuestStatus,
          guestTableSearchQuery,
          facilityId
        );
        setGuestList(regularGuests);
      } else {
        setGuestList([]);      }
    };
    loadGuests();
  },[appointmentId, patientId, shortByType, shortByDirection, appointmentGuestStatus, guestTableSearchQuery,facilityId]);

  // -------------------- Handlers --------------------
  // Handler for patientId from child
  const handleDataFromChild = (data) => setPatientId(data);

  // Handler for sorting guest table
  const handleSortClick = async (column, sortDirection) => {
    setShortByType(column?.id);
    setShortByDirection(sortDirection.toUpperCase());
  };

  // Handler for sorting denied guest table
  const handleProhibitedGuestSortClick = async (column, sortDirection) => {
    setDeniedGuestShortByType(column?.id);
    setDeniedGuestShortByDirection(sortDirection.toUpperCase());
  };

  // Handler for patient search submit
  const handlePatientSearchSubmit = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      const filteredData = PatientCardDetails.filter((patient) =>
        patient.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setSearchResults(filteredData);
      setShowPatientTable(true);
      setSelectedPatient(null);
      setIsDropdownVisible(false);
    }
  };

  // Handler for patient click
  const handlePatientClick = async (patient) => {
    setGuestList([]);
    setGuestsSecondList([]);
    setAppointmentId(patient.appointment_id);
    setPatientId(patient.patient_id);
    setSelectedPatient(patient);
    setShowPatientTable(false);
    setIsDropdownVisible(false);
    setSearchTerm("");
    setShowWalkInVisitForm(false);
    setGuestSearchTerm("");
    setGuestSearchResults([]);
    setGuestSearchPlaceholder(t('inpatient_visit.search_guest_placeholder'));
    setGuestsSecondTableSearchQuery("");

    try {
      if (patient.patient_id) {
        const regularGuests = await fetchGuestsByType(
          patient.appointment_id,
          0,
          patient.patient_id
        );
        setGuestList(regularGuests);

        const prohibitedGuests = await getDeniedGuests(
          patient.patient_id,
          2,
          deniedGuestShortByType,
          deniedGuestShortByDirection
        );
        setGuestsSecondList(prohibitedGuests);
      }
    } catch (error) {
      console.error("Error fetching guests:", error);
    }
  };

  // Handler for home button
  const handleHome = () => {
    if (selectedPatient || selectedGuest) {
      setSelectedPatient(null);
      setSelectedGuest(null);
      setPatientSearchPlaceholder(t('inpatient_visit.search_patient_placeholder'));
      setGuestSearchPlaceholder(t('inpatient_visit.search_guest_placeholder'));
    }
  };

  // Handler for patient search input change
  const handleInputChange = async (value) => {
    const selectedFacility_id = localStorage.getItem("selectedFacility");
    setSearchTerm(value);
    if (value.trim()) {
      try {
         const response = await getAppointments({
         search:     value,
          facility_id:selectedFacility_id,  
        });
        setSearchResults(response?.data || []);
        setIsDropdownVisible(true);
        setGuestTableSearchQuery("");
      } catch (error) {
        console.error("Dropdown API error:", error);
      }
    } else {
      setSearchResults([]);
      setIsDropdownVisible(false);
      setGuestSearchTerm("");
      setGuestSearchResults([]);
      setGuestSearchPlaceholder(t('inpatient_visit.search_guest_placeholder'));
      setGuestTableSearchQuery("");
    }
  };

  // Handler for guest search input change
  const handleGuestSearchInputChange = async (value) => {
    const selectedFacility_id = localStorage.getItem("selectedFacility");
    setGuestSearchTerm(value);
    if (value.trim()) {
      try {
        const response = await searchGuests({ search: value ,facility_id:selectedFacility_id, });
        const results = response.message.map(g => ({
          appointment_guest_id: g.appointment_guest_id,
          appointment_guest_screening_id: g.appointment_guest_screening_id ?? g.screening_id,
          guestName: `${g.first_name} ${g.last_name}`,
          appointmentId: g.appointment_id,
          patientName: g.patient_full_name,
          image: g.guest_image,
          mrn: g.mrn,
          patient_id: g.patient_id
        }));
       
        setGuestSearchResults(results);
        setIsGuestDropdownVisible(true);
      } catch (err) {
        console.error(err);
        setGuestSearchResults([]);
        setIsGuestDropdownVisible(false);
      }
    } else {
      setGuestSearchResults([]);
      setIsGuestDropdownVisible(false);
    }
  };

  // Handler for guest select from dropdown
  const handleGuestSelect = (guest) => {
    const patientObj = {
      appointment_id: guest.appointmentId,
      patient_id: guest.patient_id,
      name: guest.patientName || "Unknown Patient"
    };
    setGuestSearchTerm("");
    setGuestSearchResults([]);
    setGuestSearchPlaceholder(t('inpatient_visit.search_guest_placeholder'));
    setGuestTableSearchQuery(guest.guestName);
    handlePatientClick(patientObj);
    setIsGuestDropdownVisible(false);
    setGuestSearchResults([]);
  };

  // Handler for adding guest
  const handleAddGuest = async () => {
    try {
      if (!appointmentId) return;
      const regularGuests = await fetchGuestsByType(appointmentId, 0, patientId);
      setGuestList(regularGuests);
    } catch (error) {
      console.error("Error fetching regular guests after creation:", error);
    }
  };

  // Handler for adding prohibited guest
  const handleAddProhibitedGuest = async () => {
    if (!patientId) return;
    try {
      const prohibitedGuests = await getDeniedGuests(patientId, 2);
      setGuestsSecondList(prohibitedGuests);
    } catch (error) {
      console.error("Error fetching prohibited guests:", error);
    }
  };

  // Handler to toggle walk-in visit form
  const handleToggleWalkInVisitForm = () => setShowWalkInVisitForm((prev) => !prev);

  // Handler for image captured in modal
  const handleImageCaptured = (imageData) => {
    const updatedGuestList = guestList.map((guest) =>
      guest.id === selectedGuestId ? { ...guest, image: imageData } : guest
    );
    setGuestList(updatedGuestList);
    setIsModalOpen(false);
  };

  // Handler to open image modal
  const openModal = (title, guestId) => {
    setSelectedGuestId(guestId);
    setIsModalOpen(true);
  };

  // Handler for print click
  const handlePrintClick = (guest) => {
    setSelectedGuest(guest);
    setPrintModalVisible(true);
  };

  // Handler to close print modal
  const handleClosePrintModal = () => {
    setPrintModalVisible(false);
    setSelectedGuest(null);
  };

  // Handler for screening toggle
  const handleScreeningToggle = (id, newState) => {
    const updatedGuests = guestList.map((guest) =>
      guest.id === id ? { ...guest, screening: newState } : guest
    );
    setGuestList(updatedGuests);
  };

  // Handler for guest check-in
  const handleCheckIn = async (appointment_guest_id) => {
    try {
      setGuestList(prevGuests =>
        prevGuests.map(guest =>
          guest.appointment_guest_id === appointment_guest_id
            ? {
              ...guest,
              arrivalTime: new Date().toISOString(),
              departureTime: null,
            }
            : guest
        )
      );
      const response = await checkGuest(appointment_guest_id, "checkIn");
      if (response) setScreeningId(response.data?.appointment_guest_screening_id);
      const updatedGuest = transformGuest(response.data);
      setGuestList(prevGuests =>
        prevGuests.map(guest =>
          guest.appointment_guest_id === updatedGuest.appointment_guest_id
            ? { ...guest, ...updatedGuest }
            : guest
        )
      );
      if (appointmentId) {
        const regularGuests = await fetchGuestsByType(appointmentId, 0, patientId);
        setGuestList(regularGuests);
      }
    } catch (error) {
      console.error("Error during check-in:", error);
    }
  };

  // Handler for guest check-out
  const handleCheckOut = async (appointment_guest_id) => {
    try {
      setGuestList(prevGuests =>
        prevGuests.map(guest =>
          guest.appointment_guest_id === appointment_guest_id
            ? {
              ...guest,
              departureTime: new Date().toISOString(),
            }
            : guest
        )
      );
      const response = await checkGuest(appointment_guest_id, "checkOut", appointmentId);
      const updatedGuest = transformGuest(response.data);
      setGuestList(prevGuests =>
        prevGuests.map(guest =>
          guest.appointment_guest_id === updatedGuest.appointment_guest_id
            ? { ...guest, ...updatedGuest }
            : guest
        )
      );
      if (appointmentId) {
        const regularGuests = await fetchGuestsByType(appointmentId, 0, patientId);
        setGuestList(regularGuests);
      }
    } catch (error) {
      console.error("Error during check-out:", error);
    }
  };

  // Handler to open create visit modal
  const handleCreateVisitClick = () => setIsCreateVisitModalOpen(true);

  // Handler to close create visit modal
  const handleCloseCreateVisitModal = (patient) => {
    setSelectedPatient(patient);
    setIsCreateVisitModalOpen(false);
  };

  // Handler for create visit submit
  const handleCreateVisitSubmit = (formData) => {
    setAppointmentId(selectedPatient.appointment_id);
    setPatientId(selectedPatient.patient_id);
    setSelectedPatient(selectedPatient);
    setIsCreateVisitModalOpen(false);
  };

  // Handler for alert click
  const handleAlertClick = async (guest) => {
    const screeningId = guest.appointment_guest_screening_id;
    setIsAlertModalOpen(true);
    try {
      const response = await getGuestScreeningMatches(screeningId);
      const matches = response.data?.data ?? response.data ?? response;
      setAlertData(matches);
    } catch (err) {
      setAlertData([]);
    }
  };

  // Handler for alert submit
  const handleAlertSubmit = async (screeningId, reason) => {
    try {
      await overrideGuestScreening(screeningId, { reason });
      const updatedGuests = await fetchGuestsByType(appointmentId, 0, patientId);
      setGuestList(updatedGuests);
      setIsAlertModalOpen(false);
    } catch (err) {
      // handle error
    }
  };

  // Handler to close alert modal
  const handleCloseAlertModal = () => {
    setIsAlertModalOpen(false);
    setAlertData([]);
  };

  // Handler for view click in patient card
  const handelViewClick = (data) => {
    setAppointmentGuestStatus(data.appointment_guest_status);
  };

  // -------------------- Table Column Definitions --------------------
  // Get column definitions from InpatientVisitColumns
  const {
    getPatientColumns,
    getGuestColumns,
    getProhibitedGuestColumns,
  } = InpatientVisitColumns;

  const patientColumns = getPatientColumns({ t, handlePatientClick });
  const guestColumns = getGuestColumns({
    t,
    demoimg,
    formatDateTime,
    handleScreeningToggle,
    handleAlertClick,
    openModal,
    handlePrintClick,
    handleCheckIn,
    handleCheckOut,
    Cemara,
    Chat,
    Print,
    card,
    In,
    Out,
  });
  const prohibitedGuestColumns = getProhibitedGuestColumns({ t });

  // -------------------- Render --------------------
  // Main render for the component
  return (
    <div className="pl-24 pr-8 h-full">
      <div className="mb-4">
        <TabsComponent />
      </div>
      <div>
        <div className="flex flex-col sm:flex-row sm:justify-center items-center gap-4 sm:gap-6 my-4 mb-8">
          <Button type="imgbtn" className="px-[10px] py-2" icon={homeicon} onClick={handleHome} />
          <PatientSearch
            placeholder={patientSearchPlaceholder}
            searchTerm={searchTerm}
            onInputChange={handleInputChange}
            onSearchSubmit={handlePatientSearchSubmit}
            results={searchResults}
            onResultClick={handlePatientClick}
            isDropdownVisible={isDropdownVisible}
            containerRef={patientSearchRef}
          />
          <GuestSearch
            placeholder={guestSearchPlaceholder}
            searchTerm={guestSearchTerm}
            onInputChange={handleGuestSearchInputChange}
            results={guestSearchResults}
            onResultClick={handleGuestSelect}
            isDropdownVisible={isGuestDropdownVisible}
            containerRef={guestSearchRef}
            onClick={() => setIsGuestDropdownVisible(true)}
            onCreateClick={handleCreateVisitClick}
          />
        </div>
        {showPatientTable && !selectedPatient && (
          <div className="mb-8">
            <GenericTable
              title="PatientpatientCs"
              columns={patientColumns}
              showSearch={false}
              showAddButton={false}
              fixedHeader
              fixedHeaderScrollHeight="300px"
              highlightOnHover
              striped={false}
            />
          </div>
        )}
        {selectedPatient && (
          <div>
            <PatientCard
              appointmentId={selectedPatient.appointment_id}
              setPatientId={handleDataFromChild}
              patient={selectedPatient}
              PatientCard
              handelViewClick={handelViewClick}
              viewAll={!!appointmentGuestStatus}
            />
            <div className="w-full pb-10">
              {printModalVisible && selectedGuest && (
                <PrintModal guest={selectedGuest} onClose={handleClosePrintModal} />
              )}
              {isModalOpen && (
                <EditPhotoModal onClose={() => setIsModalOpen(false)} onSave={handleImageCaptured} />
              )}
              {showWalkInVisitForm && (
                <WalkInVisitForm
                  fieldsToRender={[
                    "facility",
                    "escortName",
                    "startDate",
                    "startTime",
                    "endTime",
                    "firstName",
                    "lastName",
                    "dob",
                    "guestMail",
                    "phoneNumber",
                    "relationship",
                  ]}
                  appointmentId={appointmentId}
                  onAddGuest={handleAddGuest}
                  onClose={() => setShowWalkInVisitForm(false)}
                />
              )}
              <div className="mb-6">
                <GenericTable
                  title="Guest(s)"
                  keyField="appointment_guest_id"
                  searchTerm={guestTableSearchQuery}
                  onSearchChange={(e) => setGuestTableSearchQuery(e.target.value)}
                  onAdd={handleToggleWalkInVisitForm}
                  onSort={handleSortClick}
                  columns={guestColumns}
                 data={guestList}
                  fixedHeader
                  fixedHeaderScrollHeight="300px"
                  highlightOnHover
                  striped={false}
                  noDataComponent={guestList.length > 0 ? "No records found" : null}
                />
              </div>
              {showAddGuestForm && (
                <AddProhibitedGuestForm
                  appointmentId={appointmentId}
                  patient_id={patientId}
                  onAddGuest={handleAddProhibitedGuest}
                  onClose={() => setShowAddGuestForm(false)}
                />
              )}
              <GenericTable
                title="Denied Guest(s)"
                keyField="name"
                searchTerm={guestsSecondTableSearchQuery}
                onSearchChange={(e) => setGuestsSecondTableSearchQuery(e.target.value)}
                onAdd={() => setShowAddGuestForm(true)}
                onSort={handleProhibitedGuestSortClick}
                columns={prohibitedGuestColumns}
                data={guestsSecondTableSearchQuery ? guestsSecondList : guestsSecondList}
                fixedHeader
                alwaysShowSearchInput
                fixedHeaderScrollHeight="300px"
                highlightOnHover
                striped={false}
                noDataComponent={guestsSecondList && guestsSecondList.length > 0 ? "No records found" : null}
              />
            </div>
          </div>
        )}
      </div>
      {isCreateVisitModalOpen && (
        <CreateVisitModal
          isOpen={isCreateVisitModalOpen}
          onClose={() => setIsCreateVisitModalOpen(false)}
          onSubmit={(patient) => {
            setAppointmentId(patient.appointment_id);
            setPatientId(patient.patient_id);
            setSelectedPatient(patient);
            setIsCreateVisitModalOpen(false);
          }}
        />
      )}
      {isAlertModalOpen && (
        <Alert
          isOpen={isAlertModalOpen}
          data={alertData}
          screeningId={screeningId}
          onClose={handleCloseAlertModal}
          onSubmit={handleAlertSubmit}
        />
      )}
    </div>
  );
};

export default InpatientVisit;