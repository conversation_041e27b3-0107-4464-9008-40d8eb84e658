import React, { useState, useRef, useEffect } from "react";
import GenericTable, { FilterButtons } from "../../Components/GenericTable";
import { searchPatients, getPatientInformation } from "../../api/PatientHub";
import PatientSearchNew from "./PatientSearchNew";
import { useTranslation } from 'react-i18next';

const PatientHub = () => {
  const { t } = useTranslation();
  const [filter, setFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const patientSearchRef = useRef(null);
// Whenever `filter` changes (e.g. clicking “Admitted”), clear search state:
  useEffect(() => {
    setSearchTerm("");
    setSearchResults([]);
    setIsDropdownVisible(false);
  }, [filter]);
 


  const handleInputChange = async (value) => {
  setSearchTerm(value);
  if (value.trim()) {
    try {
      const params = { search: value };
      if (filter === "admitted") {
        params.type = 0;
      }
      const response = await searchPatients(params);
      setSearchResults(response?.data?.map((p) => ({ ...p, id: p.patient_id })) || []);
      setIsDropdownVisible(true);
    } catch (error) {
      console.error("Dropdown API error:", error);
    }
  } else {
    setSearchResults([]);
    setIsDropdownVisible(false);
  }
};

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      setSearchResults(searchResults.filter((p) => p.name.toLowerCase().includes(searchTerm.toLowerCase())));
      setIsDropdownVisible(false);
    }
  };

  const handleResultClick = async (patient) => {
    try {
      const patientInfo = await getPatientInformation({ patient_id: patient.id });
      window.open(`/patient-details/${patient.id}`, "_self");
      setSearchTerm("");
      setIsDropdownVisible(false);
    } catch (error) {
      console.error("Error fetching patient information:", error);
    }
  };

  return (
    <div className="flex flex-col px-8 py-4 pl-20 pt-20">
      <h2 className="font-normal text-[24px] mb-2 text-[#4F2683]">{t('patient_hub.title')}</h2>
      <FilterButtons
        className="justify-center mb-4"
        filter={filter}
        onFilterChange={setFilter}
        buttonClass="w-[20rem] gap-10"
        filterOptions={[
          { label: t('patient_hub.filter_all'), value: "all" },
          { label: t('patient_hub.filter_admitted'), value: "admitted" }
        ]}
      />
      <PatientSearchNew
        placeholder={t('patient_hub.search_patient_placeholder')}
        searchTerm={searchTerm}
        onInputChange={handleInputChange}
        onSearchSubmit={handleSearchSubmit}
        results={searchResults}
        onResultClick={handleResultClick}
        isDropdownVisible={isDropdownVisible}
        containerRef={patientSearchRef}
      />
    </div>
  );
};

export default PatientHub;
